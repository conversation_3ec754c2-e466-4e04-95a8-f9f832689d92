[versions]
agp = "8.3.0"
kotlin = "1.9.0"
playServicesWearable = "18.0.0"
composeBom = "2023.08.00"
composeMaterial = "1.2.1"
composeFoundation = "1.2.1"
activityCompose = "1.7.2"
coreSplashscreen = "1.0.1"
preferenceVersion = "1.2.0"
wear = "1.2.0"
wearWidget = "1.0.0"
wearInput = "1.1.0"

[libraries]
androidx-preference = { module = "androidx.preference:preference", version.ref = "preferenceVersion" }
androidx-wear-input = { module = "androidx.wear:wear-input", version.ref = "wearInput" }
androidx-wear-widget = { module = "androidx.wear.widget:wear-widget", version.ref = "wearWidget" }
androidx-wear = { module = "androidx.wear:wear", version.ref = "wear" }
play-services-wearable = { group = "com.google.android.gms", name = "play-services-wearable", version.ref = "playServicesWearable" }
compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
ui = { group = "androidx.compose.ui", name = "ui" }
ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
compose-material = { group = "androidx.wear.compose", name = "compose-material", version.ref = "composeMaterial" }
compose-foundation = { group = "androidx.wear.compose", name = "compose-foundation", version.ref = "composeFoundation" }
activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
core-splashscreen = { group = "androidx.core", name = "core-splashscreen", version.ref = "coreSplashscreen" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
jetbrains-kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }

