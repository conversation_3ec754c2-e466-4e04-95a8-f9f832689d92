<?xml version="1.0" encoding="utf-8"?>
<androidx.wear.widget.BoxInsetLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/box_inset_layout_padding"
    tools:context=".presentation.MainActivity"
    tools:deviceIds="wear">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:padding="@dimen/inner_frame_layout_padding">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="32dp"
            android:background="#3F51B5"
            android:gravity="center"
            android:text="@string/sender_messages"
            android:textColor="#FFFFFF"
            android:textSize="14sp" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:fadeScrollbars="false"
            android:scrollbars="vertical"
            android:id="@+id/scrollView">

            <LinearLayout
                android:id="@+id/messageContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="8dp" />

        </ScrollView>

        <!-- 麦克风按钮和波浪动画区域 -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_marginTop="4dp"
            android:gravity="center">

            <!-- 麦克风按钮 -->
            <ImageButton
                android:id="@+id/btnMicrophone"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_centerInParent="true"
                android:background="@drawable/circular_button_background"
                android:src="@drawable/ic_mic"
                android:contentDescription="Recording microphone"
                android:scaleType="centerInside" />

            <!-- 波浪动画视图 -->
            <com.example.wearcomm.presentation.view.WaveView
                android:id="@+id/waveView"
                android:layout_width="100dp"
                android:layout_height="32dp"
                android:layout_centerInParent="true"
                android:visibility="gone" />

        </RelativeLayout>

    </LinearLayout>

</androidx.wear.widget.BoxInsetLayout>


