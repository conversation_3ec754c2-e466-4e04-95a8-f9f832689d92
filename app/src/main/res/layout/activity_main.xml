<?xml version="1.0" encoding="utf-8"?>
<androidx.wear.widget.BoxInsetLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/box_inset_layout_padding"
    tools:context=".presentation.MainActivity"
    tools:deviceIds="wear">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="@dimen/inner_frame_layout_padding">

        <Button
            android:id="@+id/btSender"
            android:layout_width="140dp"
            android:layout_height="60dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/round_button"
            android:text="@string/sender"
            android:textColor="#FFFFFF"
            android:textSize="16sp" />

        <Button
            android:id="@+id/btReceiver"
            android:layout_width="140dp"
            android:layout_height="60dp"
            android:background="@drawable/round_button"
            android:text="@string/receiver"
            android:textColor="#FFFFFF"
            android:textSize="16sp" />

    </LinearLayout>

</androidx.wear.widget.BoxInsetLayout>