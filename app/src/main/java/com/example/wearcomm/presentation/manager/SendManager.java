package com.example.wearcomm.presentation.manager;

import android.util.Log;

import com.example.wearcomm.presentation.AudioSpeaker;
import com.example.wearcomm.presentation.Constants;
import com.example.wearcomm.presentation.core.SymbolGeneration;
import com.example.wearcomm.presentation.utils.LogUtils;


public class SendManager extends Thread {

    private volatile OnSenderListener onSenderListener;

    public SendManager(OnSenderListener onSenderListener) {
        this.onSenderListener = onSenderListener;
    }

    /**
     * 断开与UI的连接，避免内存泄漏
     */
    public void disconnectListener() {
        this.onSenderListener = null;
        LogUtils.e("SendManager已断开与UI的连接");
    }

    /**
     * 重新连接UI监听器
     */
    public void reconnectListener(OnSenderListener listener) {
        this.onSenderListener = listener;
        LogUtils.e("SendManager已重新连接与UI的连接");
    }

    @Override
    public void run() {
        super.run();
        fire();
    }

    // 发送数据的核心部分
    private void fire() {
        try {
            // 定义要发送的消息ID数组：1, 2（Alice的消息）
            int[] messageIds = {1, 2};

            for (int i = 0; i < messageIds.length; i++) {
                // 检查是否被中断
                if (Thread.currentThread().isInterrupted()) {
                    LogUtils.e("发送任务被中断");
                    return;
                }

                int messageId = messageIds[i];
                String message = Constants.mmap.get(messageId);

                // 只显示发送的消息内容
                if (onSenderListener != null && message != null) {
                    onSenderListener.onSenderMessage(message);
                }

                // 设置默认的valid_bins
                int[] defaultValidBins = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15};
                sendData(messageId, defaultValidBins, 0);

                // 如果不是最后一个消息，等待1秒再发送下一个（因为每个消息前已有1秒延迟）
                if (i < messageIds.length - 1) {
                    if (!sleepMs(1000)) {
                        // 如果睡眠被中断，退出循环
                        LogUtils.e("发送任务在等待期间被中断");
                        return;
                    }
                }
            }

            LogUtils.e("完成全部发送");

            // 通知任务完成
            if (onSenderListener != null) {
                onSenderListener.onSenderCompleted();
            }
        } catch (Exception e) {
            LogUtils.e("发送任务发生异常: " + e.getMessage());
            // 即使发生异常，也要通知任务完成以清理状态
            if (onSenderListener != null) {
                onSenderListener.onSenderCompleted();
            }
        }
    }

    private void sendData(int messageId, int[] valid_bins, int m_attempt) {
        send_data_per(messageId, valid_bins, m_attempt);
    }

    private void send_data_per(int messageId, int[] valid_bins, int m_attempt) {
        // 计算比特数
        int msgbits = 16; // 初始化 msgbits 为 16，表示要发送的基本比特数量
        int traceDepth = 0; // traceDepth 是追踪深度，默认为 0，表示不添加额外比特
        msgbits += traceDepth;

        // 自适应发送
        send_data_helper(messageId, msgbits,
                valid_bins, m_attempt,
                Constants.SignalType.DataAdapt, Constants.ExpType.PER);
    }

    private void send_data_helper(int messageId, int numbits, int[] valid_bins, int m_attempt, Constants.SignalType sigType, Constants.ExpType expType) {

        short[] bits = SymbolGeneration.getCodedBits(messageId); // 获取待发送的编码比特序列。这个序列包含了数据编码后的比特信息

        // 将编码后的比特序列 bits 转换为音频信号 txsig。
        short[] txsig = SymbolGeneration.generateDataSymbols(bits, valid_bins, Constants.data_symreps, true, sigType, m_attempt);

        // 配置和播放音频信号
        AudioSpeaker audioSpeaker = new AudioSpeaker(txsig, Constants.fs, 0, txsig.length);
        audioSpeaker.play();

        // 计算信号播放时间并暂停执行确保传输完成
        int sleepTime = (int) (((double) txsig.length / Constants.fs) * 1000);
        try {
            Thread.sleep(sleepTime + Constants.SendPad);
        } catch (InterruptedException e) {
            LogUtils.e("发送数据时睡眠被中断: " + e.getMessage());
            Thread.currentThread().interrupt(); // 重新设置中断状态
            return; // 退出方法
        }
    }

    public interface OnSenderListener {
        void onSenderMessage(String message);
        void onSenderCompleted(); // 新增：任务完成回调
    }

    private boolean sleepMs(long ms) {
        try {
            Thread.sleep(ms);
            return true;
        } catch (InterruptedException e) {
            LogUtils.e("睡眠被中断: " + e.getMessage());
            Thread.currentThread().interrupt(); // 重新设置中断状态
            return false;
        }
    }
}
