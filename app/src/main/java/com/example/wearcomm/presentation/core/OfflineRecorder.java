package com.example.wearcomm.presentation.core;

import android.annotation.SuppressLint;
import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.util.Log;

import com.example.wearcomm.presentation.Constants;

import java.util.ArrayList;
import java.util.Objects;
import java.util.concurrent.locks.ReentrantLock;

public class OfflineRecorder extends Thread {
    public boolean recording;
    int samplingfrequency;
    AudioRecord rec;
    int minbuffersize;
    String filename;
    int channels;
    int read_pointer;
    int write_pointer;
    public short[] temp;

    ArrayList<Short> save_FIFO = new ArrayList<>();
    ArrayList<Short> save_FIFO2 = new ArrayList<>();
    private final ReentrantLock FIFO_lock = new ReentrantLock();

    @SuppressLint("MissingPermission")
    public OfflineRecorder(int samplingfrequency, String filename) {
        this.filename = filename;
        read_pointer = 0;
        write_pointer = 0;
        save_FIFO.clear();
        save_FIFO2.clear();

        this.samplingfrequency = samplingfrequency;
        read_pointer = 0;
        if (Constants.stereo) {
            channels = AudioFormat.CHANNEL_IN_STEREO;
        } else {
            channels = AudioFormat.CHANNEL_IN_MONO;
        }

        minbuffersize = AudioRecord.getMinBufferSize(
                samplingfrequency,
                channels,
                AudioFormat.ENCODING_PCM_16BIT);
        temp = new short[minbuffersize];

        rec = new AudioRecord(
                MediaRecorder.AudioSource.MIC,
                samplingfrequency, channels,
                AudioFormat.ENCODING_PCM_16BIT,
                minbuffersize);
    }

    /**
     * 停止当前音频录制过程。
     * 该方法会在 {@link AudioRecord} 对象处于已初始化状态时停止并释放它。
     * 如果在停止或释放过程中发生异常，方法会将异常记录到日志中。
     * 最后，方法会记录一条日志，指示停止过程正在进行中。
     * 注意：该方法不会抛出异常，而是将它们记录在标签为 "asdf" 的日志中。
     *
     * @throws IllegalStateException 如果在停止或释放时录音器不处于正确状态。
     */
    public void halt2() {
        this.recording = false;  // 设置录音状态为 false，停止录音循环

        try {
            if (this.rec.getRecordingState() == AudioRecord.STATE_INITIALIZED) {  // 检查录音设备是否已经初始化
                rec.stop();  // 如果初始化，调用 stop() 停止录音
            }
        } catch (Exception e) {
            Log.e("asdf", "halt1" + e);  // 捕获异常并记录日志，表明在停止录音时发生错误
        }

        try {
            if (this.rec.getRecordingState() == AudioRecord.STATE_INITIALIZED) {  // 再次检查录音设备是否已初始化
                rec.release();  // 如果已初始化，调用 release() 释放录音设备资源
            }
        } catch (Exception e) {
            Log.e("asdf", "halt2" + e);  // 捕获异常并记录日志，表明在释放资源时发生错误
        }
    }

    public void start2() {
        recording = true;
        start();
    }

    public short[] get_FIFO() {
        // 添加最大等待时间，避免无限等待
        int maxWaitAttempts = 50; // 最多等待1秒(50次*20ms)
        int waitCount = 0;
        
        while (read_pointer + Constants.RecorderStepSize > save_FIFO.size() && waitCount < maxWaitAttempts) {
            try {
                Thread.sleep(20);
                waitCount++;
            } catch (InterruptedException e) {
                Log.e("fifo", "等待FIFO数据时被中断: " + e.getMessage());
            }
        }
        
        // 如果等待超时，返回空数组
        if (waitCount >= maxWaitAttempts) {
            Log.e("fifo", "等待FIFO数据超时，返回空数组");
            return new short[Constants.RecorderStepSize];
        }
        
        short[] return_array = new short[Constants.RecorderStepSize];
        FIFO_lock.lock();
        try {
            // 确保不越界
            if (read_pointer + Constants.RecorderStepSize > save_FIFO.size()) {
                Log.e("fifo", "索引越界，调整读取范围: " + read_pointer + " + " + Constants.RecorderStepSize + " > " + save_FIFO.size());
                // 只读取可用的数据
                int availableData = save_FIFO.size() - read_pointer;
                if (availableData <= 0) {
                    // 如果没有可用数据，返回空数组
                    return return_array;
                }
                
                for (int i = 0; i < availableData; i++) {
                    return_array[i] = (short) save_FIFO.get(read_pointer + i);
                }
                read_pointer += availableData;
            } else {
                // 正常读取数据
                for (int i = 0; i < Constants.RecorderStepSize; i++) {
                    return_array[i] = (short) save_FIFO.get(read_pointer + i);
                }
                read_pointer += Constants.RecorderStepSize;
            }
        } catch (IndexOutOfBoundsException e) {
            Log.e("fifo", "读取FIFO数据时索引越界: " + e.getMessage());
            // 发生异常时不增加read_pointer
        } finally {
            FIFO_lock.unlock();
        }
        return return_array;
    }

    public void run() {
        fifo_read();
    }

    public void fifo_read(){
        int MAX_SAVE_FIFO = 48000 * 2;  // 定义 FIFO 队列最大容量，48000 是每秒采样率，乘以 2 表示每个采样点 2 个字节
        int remove_num = 0;  // 需要从 FIFO 队列中移除的数据量

        if (channels == AudioFormat.CHANNEL_IN_MONO) {  // 检查是否为单声道
            int bytesread;
            rec.startRecording();  // 开始录音
            while (recording) {  // 当 recording 为 true 时，保持录音状态
                bytesread = rec.read(temp, 0, minbuffersize);  // 从录音设备中读取数据到 temp 数组，bytesread 保存读取的字节数
                if (save_FIFO.size() + bytesread > MAX_SAVE_FIFO) {  // 如果队列数据量加上新读取的数据超过最大容量
                    remove_num = save_FIFO.size() + bytesread - MAX_SAVE_FIFO;  // 计算需要移除的字节数
                    if (write_pointer - remove_num < 0) {  // 如果移除的数据量超过写指针之前的数据
                        int new_length = save_FIFO.size() - write_pointer;  // 计算需要保存的数据长度
                        short[] save_buffer = new short[new_length];  // 创建新的缓冲区来保存剩余数据
                        for (int j = write_pointer; j < save_FIFO.size(); ++j) {
                            save_buffer[j - write_pointer] = save_FIFO.get(j);  // 将剩余的数据从队列复制到缓冲区
                        }

                        write_pointer = save_FIFO.size() - remove_num;  // 更新写指针，表示队列新的末尾位置
                    } else write_pointer -= remove_num;  // 如果写指针未被移除的量超出，直接更新写指针

                    FIFO_lock.lock();  // 加锁，确保线程安全
                    try {
                        save_FIFO.subList(0, remove_num).clear();  // 从队列中移除最早的 remove_num 个数据
                        read_pointer -= remove_num;  // 更新读指针
                        if (read_pointer < 0) {  // 如果读指针小于 0，说明读取速度太慢，丢失了数据
                            read_pointer = 0;  // 将读指针重置为 0
                            Log.e("asdf", "read too slow, some data lost");  // 记录数据丢失的日志
                        }
                        for (int i = 0; i < bytesread; i++) {
                            save_FIFO.add(temp[i]);  // 将新读取的数据添加到 FIFO 队列
                        }
                    } finally {
                        FIFO_lock.unlock();  // 解锁
                    }
                } else {  // 如果队列没有超出最大容量
                    FIFO_lock.lock();  // 加锁
                    try {
                        for (int i = 0; i < bytesread; i++) {
                            save_FIFO.add(temp[i]);  // 将读取到的数据直接添加到 FIFO 队列
                        }
                    } finally {
                        FIFO_lock.unlock();  // 解锁
                    }
                }
            }

            if (rec.getRecordingState() == AudioRecord.RECORDSTATE_RECORDING) {  // 如果录音设备仍在录音
                rec.stop();  // 停止录音
                rec.release();  // 释放录音设备资源
            }

            int new_length = save_FIFO.size() - write_pointer;  // 计算剩余未处理数据的长度
            short[] save_buffer = new short[new_length];  // 创建缓冲区存储未处理的数据
            for (int j = write_pointer; j < save_FIFO.size(); ++j) {
                save_buffer[j - write_pointer] = save_FIFO.get(j);  // 将剩余的数据保存到缓冲区
            }
        }

    }
}
