package com.example.wearcomm.presentation.manager;

import android.util.Log;

/**
 * 全局任务状态管理器
 * 用于防止发送和接收任务同时运行
 */
public class TaskStateManager {
    private static final String TAG = "TaskStateManager";
    private static TaskStateManager instance;
    private volatile boolean isSenderRunning = false;
    private volatile boolean isReceiverRunning = false;

    /**
     * 任务类型枚举
     */
    public enum TaskType {
        SENDER, RECEIVER
    }

    /**
     * 获取单例实例
     */
    public static synchronized TaskStateManager getInstance() {
        if (instance == null) {
            instance = new TaskStateManager();
        }
        return instance;
    }

    private TaskStateManager() {
        Log.d(TAG, "TaskStateManager初始化");
    }

    /**
     * 检查是否可以启动指定类型的任务
     * @param taskType 任务类型
     * @return true如果可以启动，false如果有冲突
     */
    public synchronized boolean canStartTask(TaskType taskType) {
        boolean canStart;
        if (taskType == TaskType.SENDER) {
            canStart = !isReceiverRunning;
            Log.d(TAG, "检查是否可以启动发送任务: " + canStart + " (接收任务运行状态: " + isReceiverRunning + ")");
        } else {
            canStart = !isSenderRunning;
            Log.d(TAG, "检查是否可以启动接收任务: " + canStart + " (发送任务运行状态: " + isSenderRunning + ")");
        }
        return canStart;
    }

    /**
     * 启动任务
     * @param taskType 任务类型
     * @return true如果启动成功，false如果有冲突
     */
    public synchronized boolean startTask(TaskType taskType) {
        if (!canStartTask(taskType)) {
            Log.e(TAG, "无法启动" + taskType + "任务，存在冲突！");
            return false;
        }

        if (taskType == TaskType.SENDER) {
            isSenderRunning = true;
            Log.d(TAG, "发送任务已启动");
        } else {
            isReceiverRunning = true;
            Log.d(TAG, "接收任务已启动");
        }
        
        logCurrentState();
        return true;
    }

    /**
     * 停止任务
     * @param taskType 任务类型
     */
    public synchronized void stopTask(TaskType taskType) {
        if (taskType == TaskType.SENDER) {
            if (isSenderRunning) {
                isSenderRunning = false;
                Log.d(TAG, "发送任务已停止");
            }
        } else {
            if (isReceiverRunning) {
                isReceiverRunning = false;
                Log.d(TAG, "接收任务已停止");
            }
        }
        
        logCurrentState();
    }

    /**
     * 检查是否有任务冲突（同时运行发送和接收）
     * @return true如果有冲突，false如果没有冲突
     */
    public synchronized boolean hasConflict() {
        boolean conflict = isSenderRunning && isReceiverRunning;
        if (conflict) {
            Log.e(TAG, "检测到任务冲突！发送任务和接收任务同时运行");
        }
        return conflict;
    }

    /**
     * 获取当前运行的任务类型
     * @return 当前运行的任务类型，如果没有任务运行则返回null
     */
    public synchronized TaskType getCurrentRunningTask() {
        if (isSenderRunning && isReceiverRunning) {
            Log.e(TAG, "异常状态：发送和接收任务同时运行");
            return null; // 冲突状态
        } else if (isSenderRunning) {
            return TaskType.SENDER;
        } else if (isReceiverRunning) {
            return TaskType.RECEIVER;
        } else {
            return null; // 没有任务运行
        }
    }

    /**
     * 强制停止所有任务（紧急情况使用）
     */
    public synchronized void forceStopAllTasks() {
        Log.w(TAG, "强制停止所有任务");
        isSenderRunning = false;
        isReceiverRunning = false;
        logCurrentState();
    }

    /**
     * 获取任务状态描述
     * @return 当前任务状态的描述字符串
     */
    public synchronized String getStatusDescription() {
        if (hasConflict()) {
            return "错误：发送和接收任务同时运行";
        } else if (isSenderRunning) {
            return "发送任务正在运行";
        } else if (isReceiverRunning) {
            return "接收任务正在运行";
        } else {
            return "没有任务运行";
        }
    }

    /**
     * 记录当前状态到日志
     */
    private void logCurrentState() {
        Log.d(TAG, "当前状态 - 发送任务: " + isSenderRunning + ", 接收任务: " + isReceiverRunning);
    }

    /**
     * 检查系统状态是否正常
     * @return true如果状态正常，false如果有异常
     */
    public synchronized boolean isSystemHealthy() {
        return !hasConflict();
    }
}
