package com.example.wearcomm.presentation;

import android.util.Log;

import com.example.wearcomm.presentation.core.Naiser;
import com.example.wearcomm.presentation.core.OfflineRecorder;
import com.example.wearcomm.presentation.core.PreambleGen;

import java.util.ArrayList;
import java.util.Random;

public class Utils {

    // Used to load the 'native-lib' library on application startup.
    static {
        System.loadLibrary("native-lib");
    }

    /**
     * 在字符串前添加前导零以补齐到指定长度。
     * <p>
     * 该方法用于将输入字符串的长度补齐到指定的最大位数（由 {Constants.maxbits} 决定）。如果输入字符串的长度小于 {Constants.maxbits}，
     * 则在字符串前添加相应数量的前导零，使其长度达到 {Constants.maxbits}。如果输入字符串的长度已经等于或大于 {Constants.maxbits}，
     * Constants.maxbits=5
     * 则不进行任何操作。
     *
     * @param str 需要补齐的输入字符串。
     * @return 补齐前导零后的字符串。
     */

    public static String pad2(String str) {
        int padlen=Constants.maxbits-str.length();
        String out = "";
        for (int i = 0; i < padlen; i++) {
            out+="0";
        }
        return out+str;
    }

    /**
     * 使用 Android 的 Log.e 方法记录给定的字符串为错误日志。
     * 日志的标签由 `Constants.LOG` 常量定义。
     *
     * @param s 要记录的字符串消息
     */

    public static void log(String s) {
        Log.e(Constants.LOG,s);
    }

    public static double[] concat(Double[] a, Double[] b) {
        double[] out = new double[a.length+b.length];
        int counter=0;
        for (Double aDouble : a) {
            out[counter++] = aDouble;
        }
        for (Double aDouble : b) {
            out[counter++] = aDouble;
        }
        return out;
    }

    public static short[] concat_short(short[] a, short[] b) {
        short[] out = new short[a.length+b.length];
        int counter=0;
        for (short value : a) {
            out[counter++] = value;
        }
        for (short value : b) {
            out[counter++] = value;
        }
        return out;
    }

    public static String trim(String s) {
        return s.substring(1,s.length()-1);
    }

    public static short[] random_array(int N) {
        Random random = new Random(1);
        short[] out = new short[N];
        for (int i = 0; i < N; i++) {
            out[i] = (short)random.nextInt(2);
        }
        return out;
    }

    public static double[] convert(short[] s) {
        double[] out = new double[s.length];
        for (int i = 0; i < s.length; i++) {
            out[i] = s[i];
        }
        return out;
    }

    /**
     * 将字符串转换为短整型数组。
     * <p>
     * 该方法接收一个二进制字符串，将字符串中的每个字符转换为相应的短整型数值，并将这些数值存储在一个 `short[]` 数组中返回。
     *
     * @param s String - 要转换的二进制字符串，每个字符应为 '0' 或 '1'。
     * @return short[] - 包含转换结果的短整型数组，其中每个元素对应字符串中的一个字符。
     * @throws NumberFormatException 如果字符串包含非 '0' 或 '1' 的字符，则可能会抛出异常。
     */

    public static short[] convert(String s) {
        short[] out = new short[s.length()];
        int counter=0;
        for (int i = 0; i < s.length(); i++) {
            out[counter++]=Short.parseShort(""+s.charAt(i));
        }
        return out;
    }

    public static Double[] convert2(short[] s) {
        Double[] out = new Double[s.length];
        for (int i = 0; i < s.length; i++) {
            out[i] = (double)s[i];
        }
        return out;
    }

    public static short[] convert_s(double[] s) {
        short[] out = new short[s.length];
        for (int i = 0; i < s.length; i++) {
            out[i] = (short)s[i];
        }
        return out;
    }

    public static String genName(Constants.SignalType sigType, int m_attempt, int chirpLoopNumber) {
        return Constants.user.toString()+"-"+sigType.toString()+"-"+m_attempt+"-"+chirpLoopNumber;
    }

    public static double min(double[] sig, int idx1, int idx2) {
        double min = 100000;
        for (int i = idx1; i < idx2; i++) {
            if (sig[i] < min) {
                min = sig[i];
            }
        }
        return min;
    }

    public static double[] max(double[] sig) {
        double max = -100000;
        int max_index = -1;
        for (int i = 0; i < sig.length; i++) {
            if (sig[i] > max) {
                max = sig[i];
                max_index = i;
            }
        }
        double[] max_info = new double[2];
        max_info[0] = max;
        max_info[1] = max_index;
        return max_info;
    }

    public static double mag2db(double data) {
        return 20*Math.log10(data);
    }

    public static double[] mag2db(double[] data) {
        double[] out = new double[data.length];
        for (int i = 0; i < out.length; i++) {
            out[i] = 20*Math.log10(data[i]);
        }
        return out;
    }

    /**
     * 从输入的数组中提取指定范围的子数组。
     * <p>
     * 该方法从输入的 `data` 数组中提取从索引 `i` 到索引 `j`（包括 `i` 和 `j`）之间的元素，并返回一个新的数组。
     *
     * @param data double[] - 输入的数组，包含要提取的数据。
     * @param i int - 子数组提取的起始索引。
     * @param j int - 子数组提取的结束索引（包含此索引的元素）。
     * @return double[] - 返回包含从索引 `i` 到 `j` 的元素的子数组。
     * @throws ArrayIndexOutOfBoundsException 如果 `i` 或 `j` 超出了 `data` 数组的有效索引范围。
     */
    public static double[] segment(double[] data, int i, int j) {
        double[] out = new double[j-i+1];
        int counter=0;
        for (int k = i; k <= j; k++) {
            out[counter++] = data[k];
        }
        return out;
    }

    public static short[] segment(short[] data, int i, int j) {
        short[] out = new short[j-i+1];
        int counter=0;
        for (int k = i; k <= j; k++) {
            out[counter++] = data[k];
        }
        return out;
    }

    public static int[] freqs2bins(int[] freqs) {
        int[] bins = new int[freqs.length];
        int freqSpacing = Constants.fs/Constants.Ns;
        for (int i = 0; i < freqs.length; i++) {
            bins[i] = freqs[i]/freqSpacing;
        }
        return bins;
    }

    public static double[] xcorr_online(double[] preamble, double[] filt) {
        double[] corr = xcorr_helper(preamble,filt);
        return evalSegv2(filt, corr);
    }

    public static double[] evalSegv2(double[] filt, double[] corr) {
        int[] cands=Utils.getCandidateLocs(corr);

        double max=0;
        int maxidx=0;
        for (int cand : cands) {
            int idx = (transform_idx(cand, filt.length));
            double[] legit2 = Naiser.Naiser_check_valid(filt, idx);
            if (legit2[1] > max) {
                max = legit2[1];
                maxidx = idx;
            }
            if (legit2[0] > 0) {
                return new double[]{corr[cand], idx, legit2[1]};
            }
        }
        return new double[]{-1,maxidx,max};
    }

    public static short[] flip(short[] bits) {
        short[] flipped = new short[bits.length];
        for (int i = 0; i < bits.length; i++) {
            if (bits[i] == 0) {
                flipped[i] = 1;
            }
        }
        return flipped;
    }

    /**
     * 等待并处理指定类型的啁啾信号。
     * 此方法监听特定类型的信号（探测、反馈或数据接收），并提取相应的啁啾数据。
     * 它基于输入参数处理录制、过滤、互相关和信号提取过程。如果检测到有效的啁啾信号，
     * 则返回经过滤波的信号；否则，返回 {@code null}。
     *
     * @param sigType 信号类型，定义在 {@link Constants.SignalType} 中。可以是 {@code Sounding}、{@code Feedback} 或 {@code DataRx}。
     * @param m_attempt 当前尝试的次数，用于生成文件名和日志记录。
     * @param chirpLoopNumber 啁啾信号的循环次数，用于生成文件名和日志记录。
     * @return 如果检测到有效的信号，返回一个包含滤波后信号的数组；否则，返回 {@code null}。
     */
    public static double[] waitForChirp(Constants.SignalType sigType, int m_attempt, int chirpLoopNumber) {
        String filename = Utils.genName(sigType, m_attempt, chirpLoopNumber);
        Log.e("fifo", filename);

        try {
            // 初始化录音设备
            if (Constants._OfflineRecorder == null) {
                Constants._OfflineRecorder = new OfflineRecorder(Constants.fs, filename);
                Constants._OfflineRecorder.start2();
            }
        } catch (Exception e) {
            Log.e("error", "初始化录音设备失败: " + e.getMessage());
            return null;
        }

        final int MAX_WINDOWS = 10;
        int numWindowsLeft = 0;
        int ChirpSamples = (int)((Constants.preambleTime/1000.0)*Constants.fs);
        double timeout = 20;
        int len = ChirpSamples;
        if (sigType.equals(Constants.SignalType.Sounding)) {
            if (Constants.exp_num == 1) {
                timeout = 15;
            }
            else if (Constants.exp_num==5) {
                if (Constants.Ns==960||Constants.Ns==1920) {
                    timeout = 3;
                }
            }
            len = ChirpSamples+Constants.ChirpGap+((Constants.Ns+Constants.Cp)*32);
        }

        int N = (int)(timeout*(Constants.fs/Constants.RecorderStepSize));
        double[] tx_preamble = PreambleGen.preamble_d();

        ArrayList<Double[]> sampleHistory = new ArrayList<>();
        ArrayList<Double> valueHistory = new ArrayList<>();
        ArrayList<Double> idxHistory = new ArrayList<>();
        int synclag = 12000;
        double[] sounding_signal;
        sounding_signal = new double[(MAX_WINDOWS+1)*Constants.RecorderStepSize];
        Log.e("len","sig length "+sounding_signal.length+","+ sigType);
        boolean valid_signal = false;
        int sounding_signal_counter=0;
        
        try {
            for (int i = 0; i < N; i++) {
                // 添加安全检查
                if (Constants._OfflineRecorder == null) {
                    Log.e("error", "OfflineRecorder not initialized");
                    return null;
                }
                
                Double[] rec;
                try {
                    rec = Utils.convert2(Constants._OfflineRecorder.get_FIFO());
                } catch (IndexOutOfBoundsException e) {
                    Log.e("error", "Index out of bounds in get_FIFO: " + e.getMessage());
                    return null;
                } catch (Exception e) {
                    Log.e("error", "Error getting FIFO data: " + e.getMessage());
                    return null;
                }

                if (sigType.equals(Constants.SignalType.Sounding)||
                        sigType.equals(Constants.SignalType.Feedback)||
                        sigType.equals(Constants.SignalType.DataRx)) {

                    if (i<MAX_WINDOWS) {
                        sampleHistory.add(rec);
                    }
                    else {
                        if (numWindowsLeft==0) {
                            double[] out;
                            
                            // 确保sampleHistory不为空且包含足够元素
                            if (sampleHistory.size() == 0) {
                                continue;
                            }
                            
                            out = Utils.concat(sampleHistory.get(sampleHistory.size() - 1), rec);

                            double[] filt = Utils.copyArray(out);
                            filt = Utils.filter(filt);

                            //value,idx
                            double[] xcorr_out = Utils.xcorr_online(tx_preamble, filt);

                            Utils.log(String.format("Listening... (%.2f)",xcorr_out[2]));

                            sampleHistory.add(rec);
                            valueHistory.add(xcorr_out[0]);
                            idxHistory.add(xcorr_out[1]);

                            if (xcorr_out[0] != -1) {
                                if (xcorr_out[1] + len + synclag > Constants.RecorderStepSize*MAX_WINDOWS) {
                                    Log.e("copy","one more flag "+xcorr_out[1]+","+(xcorr_out[1] + len + synclag));

                                    numWindowsLeft = MAX_WINDOWS-1;

                                    // 添加安全检查
                                    int startIdx = (int)xcorr_out[1];
                                    if (startIdx < 0) startIdx = 0;
                                    if (startIdx >= out.length) startIdx = out.length - 1;
                                    
                                    int endIdx = Math.min(out.length, startIdx + sounding_signal.length);
                                    
                                    for (int j = startIdx; j < endIdx && sounding_signal_counter < sounding_signal.length; j++) {
                                        sounding_signal[sounding_signal_counter++]=out[j];
                                    }

                                    Log.e("copy", "copy ("+xcorr_out[1]+","+filt.length+") to ("+sounding_signal_counter+")");
                                } else {
                                    Log.e("copy","good! "+filt.length+","+xcorr_out[1]+","+filt.length);
                                    int counter=0;
                                    
                                    // 添加安全检查
                                    int startIdx = (int)xcorr_out[1];
                                    if (startIdx < 0) startIdx = 0;
                                    if (startIdx >= out.length) startIdx = out.length - 1;
                                    
                                    for (int k = startIdx; k < out.length && counter < sounding_signal.length; k++) {
                                        sounding_signal[counter++] = out[k];
                                    }
                                    valid_signal = true;
                                    break;
                                }
                            }
                        }
                        else if (sounding_signal_counter>0){
                            Log.e("copy","another window from "+sounding_signal_counter+","+(sounding_signal_counter+rec.length)+","+sounding_signal.length);
                            
                            // 添加安全检查
                            int maxCopy = Math.min(rec.length, sounding_signal.length - sounding_signal_counter);
                            for (int i2 = 0; i2 < maxCopy; i2++) {
                                sounding_signal[sounding_signal_counter++] = rec[i2];
                            }
                            
                            numWindowsLeft -= 1;
                            if (numWindowsLeft==0){
                                valid_signal=true;
                                break;
                            }
                        }

                        if(sampleHistory.size() >= 6){
                            sampleHistory.remove(0);
                            valueHistory.remove(0);
                            idxHistory.remove(0);
                        }
                    }
                }
            }

            if (Constants._OfflineRecorder != null) {
                Constants._OfflineRecorder.halt2();
            }

            if (valid_signal) {
                return Utils.filter(sounding_signal);
            }
            return null;
        } catch (Exception e) {
            Log.e("error", "Error in waitForChirp: " + e.getMessage());
            if (Constants._OfflineRecorder != null) {
                try {
                    Constants._OfflineRecorder.halt2();
                } catch (Exception ex) {
                    Log.e("error", "Error stopping recorder: " + ex.getMessage());
                }
            }
            return null;
        }
    }

    public static int[] getCandidateLocs(double[] corr) {
        double[] corr2 = Utils.copyArray(corr);
        int MAX_CANDS = 3;
        int[] maxvals=new int[MAX_CANDS];
        for (int i = 0; i < MAX_CANDS; i++) {
            double[] maxs=Utils.max(corr2);
            maxvals[i] = (int)maxs[1];
            int idx1 = Math.max(0,(int)maxs[1]-2400);
            int idx2 = Math.min(corr.length-1,(int)maxs[1]+2400);
            for (int j = idx1; j < idx2; j++) {
                corr2[j]=0;
            }
        }
        return maxvals;
    }

    public static double[] xcorr_helper(double[] preamble, double[] sig) {
        double[][] a = Utils.fftcomplexoutnative_double(preamble, sig.length);
        double[][] b = Utils.fftcomplexoutnative_double(sig, sig.length);
        Utils.conjnative(b);
        double[][] multout = Utils.timesnative(a, b);
        return Utils.ifftnative(multout);
    }

    public static int transform_idx(int maxidx, int sig_len) {
        return sig_len-(maxidx*2)-1;
    }

    public static double[] filter(double[] sig) {
        return firwrapper(sig);
    }

    public static double[] firwrapper(double[] sig) {
        double[] h=new double[]{0.000182981959336989,0.000281596242979397,0.000278146045925432,0.000175593510303356,-4.62343304809110e-19,-0.000200360419689133,-0.000360951066953434,-0.000412991328953827,-0.000300653514269367,1.50969613210241e-18,0.000465978441137468,0.00102258147190892,0.00155366635073282,0.00192784355834049,0.00203610307379658,0.00183119111593167,0.00135603855040258,0.000749280342023432,0.000220958680427305,-2.30998316092680e-19,0.000264751350126403,0.00107567949517942,0.00233229627684471,0.00377262258405226,0.00502311871694995,0.00569227410155340,0.00548603174290836,0.00431270688583350,0.00234309825736876,0,-0.00213082326080853,-0.00345315860003227,-0.00353962427138790,-0.00228740687362881,3.04497082814396e-18,0.00264042059820580,0.00471756289619728,0.00531631859929438,0.00379212348265709,-1.99178361782373e-17,-0.00558925290045386,-0.0119350370250123,-0.0176440086545525,-0.0213191620435004,-0.0219592383672450,-0.0193021014035209,-0.0140079988312124,-0.00761010753938763,-0.00221480214028708,-3.05997847316821e-18,-0.00261988600696407,-0.0106615919949044,-0.0233019683686087,-0.0382766989959210,-0.0522058307502582,-0.0612344107673335,-0.0618649720104803,-0.0518011604210193,-0.0306049219949348,2.05563094310381e-17,0.0362729247397242,0.0730450660051671,0.104645449260725,0.125975754818137,0.133506082359307,0.125975754818137,0.104645449260725,0.0730450660051671,0.0362729247397242,2.05563094310381e-17,-0.0306049219949348,-0.0518011604210193,-0.0618649720104803,-0.0612344107673335,-0.0522058307502582,-0.0382766989959210,-0.0233019683686087,-0.0106615919949044,-0.00261988600696407,-3.05997847316821e-18,-0.00221480214028708,-0.00761010753938763,-0.0140079988312124,-0.0193021014035209,-0.0219592383672450,-0.0213191620435004,-0.0176440086545525,-0.0119350370250123,-0.00558925290045386,-1.99178361782373e-17,0.00379212348265709,0.00531631859929438,0.00471756289619728,0.00264042059820580,3.04497082814396e-18,-0.00228740687362881,-0.00353962427138790,-0.00345315860003227,-0.00213082326080853,0,0.00234309825736876,0.00431270688583350,0.00548603174290836,0.00569227410155340,0.00502311871694995,0.00377262258405226,0.00233229627684471,0.00107567949517942,0.000264751350126403,-2.30998316092680e-19,0.000220958680427305,0.000749280342023432,0.00135603855040258,0.00183119111593167,0.00203610307379658,0.00192784355834049,0.00155366635073282,0.00102258147190892,0.000465978441137468,1.50969613210241e-18,-0.000300653514269367,-0.000412991328953827,-0.000360951066953434,-0.000200360419689133,-4.62343304809110e-19,0.000175593510303356,0.000278146045925432,0.000281596242979397,0.000182981959336989};
        double[] filtout = fir(sig,h);
        return Utils.segment(filtout,63,filtout.length-1);
    }

    public static int[] arange(int i, int j) {
        int[] out = new int[j-i+1];
        for (int k = 0; k < out.length; k++) {
            out[k]=i+k;
        }
        return out;
    }

    public static double[] copyArray(double[] sig) {
        double[] out = new double[sig.length];
        System.arraycopy(sig, 0, out, 0, sig.length);
        return out;
    }

    public static double[] div(double[] sig, double val) {
        double[] out = new double[sig.length];
        for (int i = 0; i < sig.length; i++) {
            out[i] = sig[i]/val;
        }
        return out;
    }
    public static int[] linspace(int start, int inc, int end) {
        int num=(end-start)/inc;
        int[] out = new int[num];
        for (int i = 0; i < num; i++) {
            out[i] = start+(inc*i);
        }
        return out;
    }

    public static double sum_multiple(double[] a, double[] b) {
        double out = 0;
        for (int i = 0; i < a.length; i++) {
            out += a[i]*b[i];
        }
        return out;
    }

    /**
     * A native method that is implemented by the 'native-lib' native library,
     * which is packaged with this application.
     */
    public static native String decode(String bits, int poly1, int poly2, int constraint);
    public static native String encode(String bits, int poly1, int poly2, int constraint);
    public static native double[] fftnative_double(double[] data, int N);
    public static native double[] fftnative_short(short[] data, int N);
    public static native double[][] fftcomplexoutnative_double(double[] data, int N);
    public static native double[] ifftnative(double[][] data);
    public static native double[][] ifftnative2(double[][] data);
    public static native void conjnative(double[][] data);
    public static native double[][] timesnative(double[][] data1,double[][] data2);
    public static native double[][] dividenative(double[][] data1,double[][] data2);
    public static native double[] fir(double[] data, double[] h);
}
