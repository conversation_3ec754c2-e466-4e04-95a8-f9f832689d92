package com.example.wearcomm.presentation;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;


import androidx.activity.ComponentActivity;

import com.example.wearcomm.R;
import com.example.wearcomm.presentation.activity.ReceiverActivity;
import com.example.wearcomm.presentation.activity.SenderActivity;
import com.example.wearcomm.presentation.manager.TaskStateManager;

public class MainActivity extends ComponentActivity {
    private static final String TAG = "MainActivity";
    private TaskStateManager taskStateManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        taskStateManager = TaskStateManager.getInstance();
        initView();

        Log.d(TAG, "MainActivity创建完成，当前任务状态: " + taskStateManager.getStatusDescription());
    }

    private void initView() {
        findViewById(R.id.btSender).setOnClickListener(v -> startSenderWithCheck());
        findViewById(R.id.btReceiver).setOnClickListener(v -> startReceiverWithCheck());
    }

    /**
     * 检查冲突后启动发送任务
     */
    private void startSenderWithCheck() {
        Log.d(TAG, "用户点击发送按钮");

        if (!taskStateManager.canStartTask(TaskStateManager.TaskType.SENDER)) {
            String errorMessage = "无法启动发送任务！\n\n" +
                                "当前状态: " + taskStateManager.getStatusDescription() + "\n\n" +
                                "发送和接收任务不能同时运行。\n" +
                                "程序将退出以避免冲突。";

            Log.e(TAG, "检测到任务冲突，无法启动发送任务");
            showErrorAndExit(errorMessage);
            return;
        }

        Log.d(TAG, "启动发送任务");
        startActivity(new Intent(MainActivity.this, SenderActivity.class));
    }

    /**
     * 检查冲突后启动接收任务
     */
    private void startReceiverWithCheck() {
        Log.d(TAG, "用户点击接收按钮");

        if (!taskStateManager.canStartTask(TaskStateManager.TaskType.RECEIVER)) {
            String errorMessage = "无法启动接收任务！\n\n" +
                                "当前状态: " + taskStateManager.getStatusDescription() + "\n\n" +
                                "发送和接收任务不能同时运行。\n" +
                                "程序将退出以避免冲突。";

            Log.e(TAG, "检测到任务冲突，无法启动接收任务");
            showErrorAndExit(errorMessage);
            return;
        }

        Log.d(TAG, "启动接收任务");
        startActivity(new Intent(MainActivity.this, ReceiverActivity.class));
    }

    /**
     * 显示错误消息并直接退出程序
     */
    private void showErrorAndExit(String errorMessage) {
        Log.e(TAG, "任务冲突错误: " + errorMessage);

        // 强制停止所有任务
        taskStateManager.forceStopAllTasks();

        // 显示Toast错误消息
        android.widget.Toast.makeText(this, "程序退出",
                                     android.widget.Toast.LENGTH_LONG).show();

        // 直接退出程序
        System.exit(1);
    }


}