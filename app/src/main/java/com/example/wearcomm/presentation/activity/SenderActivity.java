package com.example.wearcomm.presentation.activity;

import android.os.Bundle;
import android.os.Handler;
import android.os.PowerManager;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.activity.ComponentActivity;
import androidx.annotation.Nullable;

import com.example.wearcomm.R;
import com.example.wearcomm.presentation.manager.SendManager;
import com.example.wearcomm.presentation.manager.TaskStateManager;
import com.example.wearcomm.presentation.view.WaveView;

public class SenderActivity extends ComponentActivity implements SendManager.OnSenderListener {
    private static final String TAG = "SenderActivity";
    private LinearLayout messageContainer;
    private ScrollView scrollView;
    private ImageButton btnMicrophone;
    private WaveView waveView;
    private PowerManager.WakeLock wakeLock;
    private Thread sendingThread;
    private SendManager sendManager;
    private TaskStateManager taskStateManager;
    private boolean isReturningToMain = false; // 标志位：是否返回到角色选择界面
    private boolean isRecording = false; // 录音状态
    private boolean isTaskCompleted = false; // 标志位：任务是否已完成

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_sender);

        taskStateManager = TaskStateManager.getInstance();

        // 检查是否存在任务冲突
        if (!taskStateManager.canStartTask(TaskStateManager.TaskType.SENDER)) {
            String errorMessage = "发送任务启动失败！\n\n" +
                                "当前状态: " + taskStateManager.getStatusDescription() + "\n\n" +
                                "发送和接收任务不能同时运行。\n" +
                                "程序将退出以避免冲突。";

            Log.e(TAG, "检测到任务冲突，无法启动发送任务");
            showErrorAndExit(errorMessage);
            return;
        }

        initView();

        // 获取WakeLock
        PowerManager powerManager = (PowerManager) getSystemService(POWER_SERVICE);
        wakeLock = powerManager.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, "WearComm:SenderWakeLock");

        // 直接开始发送流程
        startSendingProcess();
    }

    private void initView() {
        messageContainer = findViewById(R.id.messageContainer);
        scrollView = findViewById(R.id.scrollView);
        btnMicrophone = findViewById(R.id.btnMicrophone);
        waveView = findViewById(R.id.waveView);

        // 设置麦克风按钮点击事件
        btnMicrophone.setOnClickListener(v -> toggleRecording());

        // 设置波浪动画停止监听器
        waveView.setOnAnimationStoppedListener(() -> {
            Log.d("SenderActivity", "收到动画停止回调");
            // 确保在UI线程中更新界面
            runOnUiThread(() -> {
                Log.d("SenderActivity", "开始恢复UI状态");
                // 动画自动停止后，恢复到麦克风按钮状态
                isRecording = false;
                waveView.setVisibility(View.GONE);
                btnMicrophone.setVisibility(View.VISIBLE);
                Log.d("SenderActivity", "UI状态已恢复: waveView=GONE, btnMicrophone=VISIBLE, isRecording=" + isRecording);
            });
        });
    }

    private void startSendingProcess() {
        // 停止之前的发送任务（如果存在）
        stopSendingProcess();

        // 注册发送任务状态
        if (!taskStateManager.startTask(TaskStateManager.TaskType.SENDER)) {
            String errorMessage = "发送任务注册失败！\n\n" +
                                "当前状态: " + taskStateManager.getStatusDescription() + "\n\n" +
                                "发送和接收任务不能同时运行。\n" +
                                "程序将退出以避免冲突。";

            Log.e(TAG, "发送任务注册失败，存在冲突");
            showErrorAndExit(errorMessage);
            return;
        }

        // 在后台线程中执行等待和发送
        sendingThread = new Thread(() -> {
            try {
                // 获取WakeLock
                wakeLock.acquire();

                // 添加日志记录
                Log.d(TAG, "发送端将在6秒后开启");

                // 等待5秒（静默期），检查中断
                for (int i = 0; i < 60; i++) {
                    if (Thread.currentThread().isInterrupted()) {
                        Log.d(TAG, "发送端在等待期间被中断");
                        return;
                    }
                    Thread.sleep(100); // 分成小段睡眠，便于响应中断
                }

                // 检查是否被中断
                if (Thread.currentThread().isInterrupted()) {
                    Log.d(TAG, "发送端在启动前被中断");
                    return;
                }

                // 启动发送管理器，并以当前活动作为监听器
                sendManager = new SendManager(this);
                sendManager.start();

                // 使用循环检查的方式替代join()，以便能够响应中断
                while (sendManager != null && sendManager.isAlive()) {
                    if (Thread.currentThread().isInterrupted()) {
                        Log.d(TAG, "发送端在执行期间被中断");
                        if (sendManager != null) {
                            sendManager.interrupt(); // 确保SendManager也被中断
                        }
                        return;
                    }
                    Thread.sleep(100); // 短暂睡眠后再检查
                }

                Log.d(TAG, "发送端任务完成");

            } catch (InterruptedException e) {
                Log.d(TAG, "发送端任务被中断: " + e.getMessage());
                // 确保SendManager也被中断
                if (sendManager != null) {
                    sendManager.interrupt();
                }
            } catch (Exception e) {
                Log.e(TAG, "发送端任务失败: " + e.getMessage());
                e.printStackTrace();
            } finally {
                // 只有在任务被中断或异常时才在这里清理资源
                // 正常完成的情况下，资源会在onSenderCompleted中清理
                if (Thread.currentThread().isInterrupted() || !isTaskCompleted) {
                    // 释放WakeLock
                    if (wakeLock != null && wakeLock.isHeld()) {
                        wakeLock.release();
                        Log.d(TAG, "WakeLock已释放（任务中断或异常）");
                    }

                    // 注销发送任务状态
                    taskStateManager.stopTask(TaskStateManager.TaskType.SENDER);
                    Log.d(TAG, "发送任务状态已注销（任务中断或异常）");
                }
            }
        });
        sendingThread.start();
    }

    private void stopSendingProcess() {
        // 中断发送线程
        if (sendingThread != null && sendingThread.isAlive()) {
            sendingThread.interrupt();
            Log.d(TAG, "正在停止发送线程");

            // 不等待线程结束，立即返回以确保快速响应
            // sendingThread.join(); // 移除这个等待，让线程自然结束
        }

        // 中断发送管理器
        if (sendManager != null) {
            sendManager.interrupt();
            Log.d(TAG, "正在停止发送管理器");
        }

        // 注销发送任务状态
        if (taskStateManager != null) {
            taskStateManager.stopTask(TaskStateManager.TaskType.SENDER);
            Log.d(TAG, "发送任务状态已注销");
        }
    }



    @Override
    protected void onResume() {
        super.onResume();

        // 重置返回标志位
        isReturningToMain = false;

        // 检查任务状态并更新UI
        updateUIFromBackgroundState();

        Log.d(TAG, "从后台返回，已更新UI状态");
    }

    @Override
    protected void onPause() {
        super.onPause();

        // 检查Activity是否正在结束（包括按返回键、滑动返回等所有情况）
        if (isFinishing()) {
            // Activity正在结束，说明用户要返回主界面
            isReturningToMain = true;
            Log.d(TAG, "检测到Activity正在结束，标记为返回主界面");

            // 立即停止发送任务
            stopSendingProcess();

            // 释放WakeLock
            if (wakeLock != null && wakeLock.isHeld()) {
                wakeLock.release();
                Log.d(TAG, "WakeLock已释放");
            }
        } else {
            // Activity只是暂停（如Home键），断开UI连接但保持任务运行
            if (sendManager != null) {
                sendManager.disconnectListener();
                Log.d(TAG, "暂停时断开发送管理器与UI的连接");
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        if (isReturningToMain) {
            // 返回到角色选择界面：停止所有任务并释放资源
            Log.d(TAG, "返回到角色选择界面，停止所有发送任务");
            stopSendingProcess();

            // 释放WakeLock
            if (wakeLock != null && wakeLock.isHeld()) {
                wakeLock.release();
            }
        } else if (isTaskCompleted) {
            // 任务已完成：无需额外处理，资源已在onSenderCompleted中清理
            Log.d(TAG, "任务已完成，资源已清理");
        } else {
            // 其他所有退出场景：按退出到系统处理，任务继续后台运行
            Log.d(TAG, "退出到系统，任务继续后台运行");

            // 断开SendManager与UI的连接，避免内存泄漏
            if (sendManager != null) {
                sendManager.disconnectListener();
                Log.d(TAG, "断开发送管理器与UI的连接");
            }

            // 不释放WakeLock，让任务继续在后台运行
            // 注意：不注销任务状态，因为任务继续在后台运行
            Log.d(TAG, "保持WakeLock，任务继续后台运行");
        }
    }

    /**
     * 从后台状态更新UI
     */
    private void updateUIFromBackgroundState() {
        // 检查任务状态管理器中的发送任务状态
        if (taskStateManager.getCurrentRunningTask() == TaskStateManager.TaskType.SENDER) {
            Log.d(TAG, "检测到发送任务正在后台运行，重新连接UI");

            // 如果发送任务正在运行，重新建立SendManager与UI的连接
            if (sendManager != null && !isTaskCompleted) {
                // 重新连接监听器，恢复UI更新
                sendManager.reconnectListener(this);
                Log.d(TAG, "重新建立发送管理器与UI的连接");

                // 更新UI状态
                runOnUiThread(() -> {
                    // 可以在这里添加"发送中..."的状态显示
                    Log.d(TAG, "UI已更新以反映后台发送任务状态");
                });
            }
        } else {
            Log.d(TAG, "没有检测到后台发送任务");
        }
    }

    /**
     * 切换录音状态
     */
    private void toggleRecording() {
        if (isRecording) {
            // 停止录音
            stopRecording();
        } else {
            // 开始录音
            startRecording();
        }
    }

    /**
     * 开始录音
     */
    private void startRecording() {
        isRecording = true;

        // 隐藏麦克风按钮，显示波浪动画
        btnMicrophone.setVisibility(View.GONE);
        waveView.setVisibility(View.VISIBLE);
        waveView.startAnimation();

        Log.d("SenderActivity", "开始录音");
    }

    /**
     * 停止录音
     */
    private void stopRecording() {
        isRecording = false;

        // 停止波浪动画，显示麦克风按钮
        waveView.stopAnimation();
        waveView.setVisibility(View.GONE);
        btnMicrophone.setVisibility(View.VISIBLE);

        Log.d("SenderActivity", "停止录音");
    }

    @Override
    public void onSenderMessage(String message) {
        // 在UI线程添加新的消息项
        runOnUiThread(() -> {
            if (messageContainer != null) {
                // 创建新的消息视图
                LayoutInflater inflater = LayoutInflater.from(this);
                View messageView = inflater.inflate(R.layout.message_sent_item, messageContainer, false);
                TextView tvMessageText = messageView.findViewById(R.id.tvMessageText);
                tvMessageText.setText(message);

                // 添加到容器中
                messageContainer.addView(messageView);

                // 自动滚动到底部
                scrollView.post(() -> scrollView.fullScroll(View.FOCUS_DOWN));

                Log.d("SenderActivity", "发送消息: " + message);
            }
        });
    }

    @Override
    public void onSenderCompleted() {
        // 发送任务完成，标记任务已完成
        Log.d(TAG, "发送任务已完成（通过回调通知）");
        isTaskCompleted = true;

        // 立即清理任务状态和资源
        runOnUiThread(() -> {
            // 注销发送任务状态
            if (taskStateManager != null) {
                taskStateManager.stopTask(TaskStateManager.TaskType.SENDER);
                Log.d(TAG, "发送任务状态已注销（任务完成）");
            }

            // 释放WakeLock
            if (wakeLock != null && wakeLock.isHeld()) {
                wakeLock.release();
                Log.d(TAG, "WakeLock已释放（任务完成）");
            }

            // 清理SendManager引用
            sendManager = null;
        });
    }

    /**
     * 显示错误消息并直接退出程序
     */
    private void showErrorAndExit(String errorMessage) {
        Log.e(TAG, "任务冲突错误: " + errorMessage);

        // 强制停止所有任务
        taskStateManager.forceStopAllTasks();

        // 显示Toast错误消息
        android.widget.Toast.makeText(this, "程序退出",
                                     android.widget.Toast.LENGTH_LONG).show();

        // 直接退出程序
        System.exit(1);
    }

    @Override
    public void onBackPressed() {
        // 用户按返回键，返回到角色选择界面
        // 注意：实际的停止逻辑在onPause()中处理，这里只需要调用super
        Log.d(TAG, "用户按下返回键，返回到角色选择界面");
        super.onBackPressed();
    }
}
