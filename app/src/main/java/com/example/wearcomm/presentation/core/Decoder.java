package com.example.wearcomm.presentation.core;

import android.util.Log;

import com.example.wearcomm.presentation.Constants;
import com.example.wearcomm.presentation.Utils;


public class Decoder {
    /**
     * 解码接收到的数据信号并提取有效信息。
     * <p>
     * 该方法执行信号的解调、均衡、差分解码、维比特解码等操作，最终从接收到的数据信号中提取有效的消息 ID 并显示在用户界面上。
     *
     * @param data       double[] - 接收到的数据信号，包含需要解码的原始数据。
     * @param valid_bins int[] - 有效载波频率的索引，用于解调和解码过程。
     */
    public static String decode_helper(double[] data, int[] valid_bins) {
        data = Utils.filter(data); // 对 data 数组进行过滤处理(去除噪声，平滑数据)
        // 添加偏移量
        valid_bins[0] = valid_bins[0] + Constants.nbin1_default;
        valid_bins[1] = valid_bins[1] + Constants.nbin1_default;

        // 二进制填充顺序
        // 元素 0 => 传输数据符号数
        // 元素 1...n => 符号中对应数据位的位数（其余为填充位）
        int[] binFillOrder = SymbolGeneration.binFillOrder(Utils.arange(valid_bins[0], valid_bins[1]));

        // 从第一个 OFDM 符号中提取先导符号
        // 将其与传输的先导符号进行比较
        // 并执行频域均衡
        int ptime = (int) ((Constants.preambleTime / 1000.0) * Constants.fs);
        int start = ptime + Constants.ChirpGap;
        double[] rx_pilots = Utils.segment(data, start + Constants.Cp, start + Constants.Cp + Constants.Ns - 1);
        start = start + Constants.Cp + Constants.Ns;

        // 获取并处理传输的先导符号 tx_pilots
        double[] tx_pilots = Utils.convert(SymbolGeneration.getTrainingSymbol(Utils.arange(valid_bins[0], valid_bins[1])));
        tx_pilots = Utils.segment(tx_pilots, Constants.Cp, Constants.Cp + Constants.Ns - 1);

        // 从频域均衡中获取权重
        double[][] tx_spec = Utils.fftcomplexoutnative_double(tx_pilots, tx_pilots.length);
        double[][] rx_spec = Utils.fftcomplexoutnative_double(rx_pilots, rx_pilots.length);
        double[][] weights = Utils.dividenative(tx_spec, rx_spec);
        double[][] recovered_pilot_sym = Utils.timesnative(rx_spec, weights);

        // 差分解码
        int numsyms = binFillOrder[0]; // 数据符号数
        double[][][] symbols = new double[numsyms + 1][][];
        symbols[0] = recovered_pilot_sym;

        // 提取每个符号并用权重均衡
        for (int i = 0; i < numsyms; i++) {
            double[] sym = Utils.segment(data, start + Constants.Cp, start + Constants.Cp + Constants.Ns - 1);
            start = start + Constants.Cp + Constants.Ns;

            double[][] sym_spec = Utils.fftcomplexoutnative_double(sym, sym.length);
            sym_spec = Utils.timesnative(sym_spec, weights);
            symbols[i + 1] = sym_spec;
        }

        // 将符号解调为比特
        short[][] bits = Modulation.pskdemod_differential(symbols, valid_bins);

        // 对每个符号重新排列交错后的比特位
        // 从符号中提取与有效数据相对应的比特
        StringBuilder coded = new StringBuilder();
        for (int i = 0; i < bits.length; i++) {
            short[] newbits;
            newbits = SymbolGeneration.unshuffle(bits[i], i);
            // 提取数据位数
            for (int j = 0; j < binFillOrder[i + 1]; j++) {
                coded.append(newbits[j]);
            }
        }
        // 执行维比特解码, 涉及卷积解码，返回二进制字符串
        String uncoded = Utils.decode(coded.toString(), Constants.cc[0], Constants.cc[1], Constants.cc[2]);

        // 二进制转十进制，从比特中提取信息 ID
        int messageID = Integer.parseInt(uncoded, 2);

        Log.e("messageID:", messageID + "---");
        String message; // 程序界面显示解码后的数据，预先设定为Error
        message = Constants.mmap.get(messageID);
        Utils.log(coded + "=>" + uncoded + "=>" + message);
        return message;
    }
}
