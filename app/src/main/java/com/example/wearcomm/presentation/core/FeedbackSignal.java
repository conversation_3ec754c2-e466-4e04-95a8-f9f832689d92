package com.example.wearcomm.presentation.core;


import android.util.Log;

import com.example.wearcomm.presentation.Constants;
import com.example.wearcomm.presentation.Utils;
import com.example.wearcomm.presentation.utils.LogUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedList;

public class FeedbackSignal {
    public static int[] extractSignalHelper(double[] rec, int start_point, int m_attempt) {
        double[] preamble = PreambleGen.preamble_d();
        int end_point = start_point + preamble.length - 1;
        if (end_point - 1 > rec.length || start_point < 0) {
            Utils.log("Error extracting preamble from feedback signal");
            return new int[]{-1, -1};
        }
        double[] preamble_rx = Utils.segment(rec, start_point, end_point);

        int rec_start = start_point + preamble.length + Constants.ChirpGap + 1;
        int rec_end = rec_start + (int) ((Constants.fbackTime / 1000.0) * Constants.fs) - 1;
        int rec_len = (rec_end - rec_start) + 1;
        LogUtils.e(rec.length + "," + rec_start + "," + rec_end + "," + rec_len);

        if (rec_end - 1 > rec.length || rec_start < 0) {
            Utils.log("Error extracting feedback from feedback signal");
            return new int[]{-1, -1};
        }
        double[] feedback = Utils.segment(rec, rec_start - 1, rec_end - 1);

        int[] freqs = parse_signal(preamble_rx, feedback);
        if (freqs.length == 2 && freqs[0] != -1) {

            freqs[0] = (int) Math.ceil(freqs[0] / (double) Constants.inc) * Constants.inc;
            freqs[1] = (int) Math.floor(freqs[1] / (double) Constants.inc) * Constants.inc;

            int[] freqs_all = expand_freqs(freqs);

            return Utils.freqs2bins(freqs_all);
        } else {
            return new int[]{-1, -1};
        }
    }

    public static int[] expand_freqs(int[] freqs) {
        int freqSpacing = Constants.fs / Constants.Ns;
        int numbins = (freqs[freqs.length - 1] - freqs[0]) / freqSpacing;

        int[] out = new int[numbins + 1];
        for (int i = 0; i <= numbins; i++) {
            out[i] = freqs[0] + (i * freqSpacing);
        }
        return out;
    }

    /**
     * 编码反馈信号的方法。
     *
     * @param fbegin    起始频率索引，表示反馈信号的起始频率。
     * @param fend      结束频率索引，表示反馈信号的结束频率。
     * @param len_ms    信号的时长，以毫秒为单位。
     * @param preamble  是否包含前导码（preamble），如果为true，则信号前面会加上前导码。
     * @param m_attempt 当前尝试的次数，用于某些处理或记录用途。
     * @return 编码后的短整型数组，表示生成的反馈信号。
     * <p>
     * 该方法根据输入参数生成反馈信号，并可选择性地在信号前加上前导码。生成的信号被编码为一个短整型数组返回。
     * 方法的主要流程如下：
     * <p>
     * 1. 计算信号的长度len（以采样点为单位），如果需要前导码，还会加上前导码的长度和一个间隙。
     * 2. 创建一个短整型数组txsig用于存储生成的信号。
     * 3. 如果需要前导码，将前导码的值添加到txsig中，并跳过前导码后的间隙位置。
     * 4. 根据输入的频率索引fbegin和fend，计算实际的频率值，并对频率进行10的倍数取整。
     * 5. 生成反馈信号的频率数组freqs，并将信号添加到txsig数组中。
     * 6. 提取生成信号的反馈部分，并进行傅里叶变换，得到频谱数据。
     * 7. 在用户界面（UI）线程上绘制频谱图，用于显示生成的反馈信号频谱。
     * 8. 最后，返回生成的信号数组txsig。
     */

    public static short[] encodeFeedbackSignal(int fbegin, int fend, int len_ms, boolean preamble, int m_attempt) {
        int len = (int) ((len_ms / 1000.0) * Constants.fs);
        if (preamble) {
            len += ((Constants.preambleTime / 1000.0) * Constants.fs) + Constants.ChirpGap;
        }
        short[] txsig = new short[len];

        int counter = 0;
        if (preamble) {
            for (Short s : PreambleGen.preamble_s()) {
                txsig[counter++] = s;
            }
            counter += Constants.ChirpGap;
        }

        fbegin = Constants.f_range[0] + (fbegin * Constants.inc);
        fend = Constants.f_range[0] + (fend * Constants.inc);

        fbegin = Math.round(fbegin / 10) * 10;
        fend = Math.round(fend / 10) * 10;

        // encode the feedback frequencies
        int freqs[] = new int[]{fbegin, fend};
        int fbackLen = (int) ((Constants.fbackTime / 1000.0) * Constants.fs);
        for (int freq = 0; freq < freqs.length; freq++) {
            int ff = freqs[freq];
            for (int i = counter; i < len; i++) {
                txsig[i] += (Math.sin(2.0 * Math.PI * ff * ((double) i / Constants.fs))) * (32767 / 2);
            }
        }

        short[] feedback = new short[fbackLen];
        for (int i = 0; i < feedback.length; i++) {
            feedback[i] = txsig[counter++];
        }

        double[] spec_fback = Utils.fftnative_short(feedback, feedback.length);
        double[] spec_fback_db = Utils.mag2db(spec_fback);

        return txsig;
    }

    public static int[] parse_signal(double[] preamble, double[] feedback) {
        Utils.log("FeedbackSignal_parse_signal");

        double[] preamble_spec = Utils.fftnative_double(preamble, preamble.length);

        double[] feedback_spec = Utils.fftnative_double(feedback, feedback.length);

        double[] preamble_spec_db = Utils.mag2db(preamble_spec);
        double[] feedback_spec_db = Utils.mag2db(feedback_spec);

        int[] freqs = decodeFeedbackSignal(feedback_spec_db);

        if (freqs.length == 2) {
            Utils.log("feedback freqs " + freqs[0] + "," + freqs[freqs.length - 1]);
        } else {
            Utils.log("no frequencies selected");
        }
        return freqs;
    }

    public static int[] decodeFeedbackSignal(double[] feedback_spec_db) {

        LinkedList<Bin> bins = new LinkedList<>();
        double[] smooth_sig = feedback_spec_db;

        int feedbackFreqSpacing = Constants.fs / feedback_spec_db.length;
        int startIdx = Constants.f_range[0] / feedbackFreqSpacing;
        int endIdx = Constants.f_range[1] / feedbackFreqSpacing;

        for (int i = startIdx; i < endIdx; i++) {
            int freq = i * feedbackFreqSpacing;

            double signal = smooth_sig[i];
            double[] noise1 = Utils.segment(smooth_sig, i - 5, i - 2);
            double[] noise2 = Utils.segment(smooth_sig, i + 2, i + 5);

            double val = 0;
            for (Double d : noise1) {
                val += d;
            }
            for (Double d : noise2) {
                val += d;
            }
            double noise = val / (noise1.length + noise2.length);

            double snr = signal - noise;
            double prom = getProm(smooth_sig, i, i - 2, i + 2);

            if (snr >= Constants.FEEDBACK_SNR_THRESH) {
                bins.add(new Bin(freq, snr, signal, noise, prom));
            }
        }

        Collections.sort(bins, new Comparator<Bin>() {
            @Override
            public int compare(Bin c1, Bin c2) {
                double met1 = c1.prom + c1.snr;
                double met2 = c2.prom + c2.snr;
                if (met1 > met2) {
                    return 1;
                } else if (met1 == met2) {
                    return 0;
                } else {
                    return -1;
                }
            }
        });

        LinkedList<Integer> remove = new LinkedList<>();
        for (int i = bins.size() - 1; i >= 1; i--) {
            if (Math.abs(bins.get(i).freq - bins.get(i - 1).freq) == feedbackFreqSpacing) {
                if (bins.get(i).snr > bins.get(i - 1).snr) {
                    remove.add(i);
                } else {
                    remove.add(i - 1);
                }
            }
        }

        for (Integer i : remove) {
            bins.remove(bins.get(i));
        }

        if (bins.size() >= 2) {
            int f1 = bins.get(bins.size() - 1).freq;
            int f2 = bins.get(bins.size() - 2).freq;
            double s1 = bins.get(bins.size() - 1).snr;
            double s2 = bins.get(bins.size() - 2).snr;

            Bin nf1 = search(bins, f1 / 2);
            Bin nf2 = search(bins, f2 / 2);
            if (nf1 != null && nf1.snr > s1) {
                f1 = nf1.freq;
                s1 = nf1.snr;
            }
            if (nf2 != null && nf2.snr > s2) {
                f2 = nf2.freq;
                s2 = nf2.snr;
            }

            if (s1 >= Constants.FEEDBACK_SNR_THRESH && s2 >= Constants.FEEDBACK_SNR_THRESH) {
                if (f1 > f2) {
                    return new int[]{f2, f1};
                }
                return new int[]{f1, f2};
            }
        } else if (bins.size() == 1) {
            return new int[]{bins.get(0).freq, bins.get(0).freq};
        }
        return new int[]{-1, -1};
    }

    public static Bin search(LinkedList<Bin> bins, int freq) {
        for (Bin bin : bins) {
            if (bin.freq == freq) {
                return bin;
            }
        }
        return null;
    }

    private static class Bin {
        int freq;
        double snr;
        double signal;
        double noise;
        double prom;

        public Bin(int freq, double snr, double signal, double noise, double prom) {
            this.freq = freq;
            this.snr = snr;
            this.signal = signal;
            this.noise = noise;
            this.prom = prom;
        }
    }

    public static double getProm(double[] ar, int peakloc, int beginloc, int endloc) {
        double peakval = ar[peakloc];
        int leftmarker = 0;
        int rightmarker = endloc;
        for (int i = peakloc - 1; i >= beginloc; i--) {
            if (ar[i] >= peakval) {
                leftmarker = i;
                break;
            }
        }
        for (int i = peakloc + 1; i < endloc; i++) {
            if (ar[i] >= peakval) {
                rightmarker = i;
                break;
            }
        }

        double leftmin = ar[beginloc];
        double rightmin = ar[endloc];
        for (int i = beginloc; i < leftmarker; i++) {
            if (ar[i] < leftmin) {
                leftmin = ar[i];
            }
        }

        for (int i = rightmarker; i < endloc; i++) {
            if (ar[i] < rightmin) {
                rightmin = ar[i];
            }
        }

        double ref = leftmin > rightmin ? leftmin : rightmin;

        double out = peakval - ref;

        return out;
    }
}
