package com.example.wearcomm.presentation;

import android.content.SharedPreferences;
import androidx.preference.PreferenceManager;
import android.util.Log;

import com.example.wearcomm.R;
import com.example.wearcomm.presentation.core.FileOperations;
import com.example.wearcomm.presentation.core.OfflineRecorder;
import com.example.wearcomm.presentation.core.PreambleGen;
import com.example.wearcomm.presentation.utils.ContextUtils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;

public class Constants {
    public enum User {
        Alice,
    }

    public enum SignalType {
        Sounding,
        Feedback,
        DataRx,
        DataAdapt,
        DataFull_1000_4000,
        DataFull_1000_2500,
        DataFull_1000_1500,
    }

    public enum ExpType {
        PER
    }

    public static final boolean CODING = true;

    public static final int FEEDBACK_SNR_THRESH = 13;
    public static final int SNR_THRESH2 = 8;
    public static final int RecorderStepSize = 24000;
    public static final float NaiserThresh = .6f;
    public static final boolean DIFFERENTIAL = true;
    public static final boolean INTERLEAVE = true;
    public static final float FreAdaptScaleFactor = 0;
    public static final int maxbits = 5;
    static int exp_num = 5;
    static User user;
    static String LOG = "log";
    public final static int Ns = 960; // 符号长度
    public static final int Gi = 0;
    public final static int[] f_range = {1000, 4000}; // 频率范围
    public static final int fs = 48000;
    public static final int Cp; // 循环前缀长度
    static int Nsyms = 100;
    public static final int nbin1_default;
    public static final int nbin2_default;
    public static final int subcarrier_number_default;

    public static final int nbin1_chanest;
    static int nbin2_chanest;
    public static final int subcarrier_number_chanest;

    static int nbin1_data, nbin2_data;
    static int subcarrier_number_data;

    public final static int inc;

    public static final int snr_method = 2;

    static int sym_len;
    static int blocklen;

    public final static int ChirpGap = 960;
    static int[] valid_carrier_preamble;
    public static final int[] valid_carrier_data;
    public static final int[] valid_carrier_default;
    static LinkedList<Integer> f_seq;
    static int[] null_carrier = {};
    public static final boolean FLIP_SYMBOL = false;
    public static final boolean stereo = false;
    static OfflineRecorder _OfflineRecorder;
    public static final short[] pn60_bits = new short[]{1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 0, 1, 0, 0, 1, 1, 1, 1, 0, 1, 0, 0, 0, 1, 1, 1, 0, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 1, 0, 1, 0, 1, 1};
    public static final double[][] pn60_syms = new double[][]
            {{1, 1, 1, 1, 1}, {-1, -1, -1, -1, -1}, {-1, -1, -1, -1, -1}, {-1, -1, -1, -1, -1}, {-1, -1, -1, -1, -1},
                    {-1, -1, -1, -1, -1}, {1, 1, 1, 1, 1}, {-1, -1, -1, -1, -1}, {-1, -1, -1, -1, -1}, {-1, -1, -1, -1, -1},
                    {-1, -1, -1, -1, -1}, {1, 1, 1, 1, 1}, {1, 1, 1, 1, 1}, {-1, -1, -1, -1, -1}, {-1, -1, -1, -1, -1},
                    {-1, -1, -1, -1, -1}, {1, 1, 1, 1, 1}, {-1, -1, -1, -1, -1}, {1, 1, 1, 1, 1}, {-1, -1, -1, -1, -1},
                    {-1, -1, -1, -1, -1}, {1, 1, 1, 1, 1}, {1, 1, 1, 1, 1}, {1, 1, 1, 1, 1}, {1, 1, 1, 1, 1},
                    {-1, -1, -1, -1, -1}, {1, 1, 1, 1, 1}, {-1, -1, -1, -1, -1}, {-1, -1, -1, -1, -1}, {-1, -1, -1, -1, -1},
                    {1, 1, 1, 1, 1}, {1, 1, 1, 1, 1}, {1, 1, 1, 1, 1}, {-1, -1, -1, -1, -1}, {-1, -1, -1, -1, -1},
                    {1, 1, 1, 1, 1}, {-1, -1, -1, -1, -1}, {-1, -1, -1, -1, -1}, {1, 1, 1, 1, 1}, {-1, -1, -1, -1, -1},
                    {1, 1, 1, 1, 1}, {1, 1, 1, 1, 1}, {-1, -1, -1, -1, -1}, {1, 1, 1, 1, 1}, {1, 1, 1, 1, 1},
                    {1, 1, 1, 1, 1}, {-1, -1, -1, -1, -1}, {1, 1, 1, 1, 1}, {1, 1, 1, 1, 1}, {-1, -1, -1, -1, -1},
                    {-1, -1, -1, -1, -1}, {1, 1, 1, 1, 1}, {1, 1, 1, 1, 1}, {-1, -1, -1, -1, -1}, {1, 1, 1, 1, 1},
                    {-1, -1, -1, -1, -1}, {1, 1, 1, 1, 1}, {-1, -1, -1, -1, -1}, {1, 1, 1, 1, 1}, {1, 1, 1, 1, 1},
            };
    static int preambleStartFreq;
    static int preambleEndFreq;
    public static final int preambleTime; // milliseconds
    public static final int fbackTime = 200; // milliseconds
    public static final int SendPad = 100;
    public static final int data_symreps = 1;
    public static final int chanest_symreps = 7;
    static short[] data_nocode;
    static short[] data12;
    static short[] data23;
    static boolean NAISER = true;
    public static final HashMap<Integer, String> mmap = new HashMap<>();
    public static final int[] cc = new int[]{7, 5, 10};
    public static final double[] naiser;

    static {
        mmap.put(1, "Alice: Where are you?");
        mmap.put(2, "Alice: Tell me your coordinate");
        mmap.put(3, "Alice: I need your help");
        mmap.put(4, "Bob: Need assistance");
        mmap.put(5, "Bob: Mission complete");
        mmap.put(6, "Bob: Weak signal");
        mmap.put(7, "Good Luck");
        mmap.put(8, "Hello, Sir");
        mmap.put(9, "Good night");
        mmap.put(10, "Good Bye,See you later");
        data_nocode = FileOperations.readrawasset_binary(R.raw.data_nocode);
        data12 = FileOperations.readrawasset_binary(R.raw.encode_data_1_2);
        data23 = FileOperations.readrawasset_binary(R.raw.encode_data_2_3);
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(ContextUtils.application);

        Constants.user = User.valueOf(prefs.getString("user", User.Alice.toString()));

        int tempPreambleTime = 160;
        double[] tempNaiser = null;
        if (NAISER) {
            tempPreambleTime = 195;
            tempNaiser = FileOperations.readrawasset(R.raw.naiser3, 1);
        } else {
            if (Constants.exp_num == 5) {
                tempPreambleTime = 200;
            } else if (Constants.exp_num == 4 || Constants.exp_num == 3 || Constants.exp_num == 2 || Constants.exp_num == 1) {
                tempPreambleTime = 100;
            }
        }
        naiser = tempNaiser;

        preambleTime = tempPreambleTime;

        preambleStartFreq = f_range[0];
        preambleEndFreq = f_range[1];

        if (Constants.Ns == 960) {
            Cp = 67;
        }
        inc = fs / Ns;
        sym_len = Ns + Cp + Gi;

        nbin1_data = Math.round((float)f_range[0] / ((float)inc / data_symreps));
        nbin2_data = Math.round(f_range[1] / ((float)inc / data_symreps)) - 1;
        subcarrier_number_data = (nbin2_data - nbin1_data + 1) / data_symreps;

        nbin1_chanest = nbin1_data * chanest_symreps;
        nbin2_chanest = nbin1_chanest + (subcarrier_number_data * chanest_symreps);
        subcarrier_number_chanest = (nbin2_chanest - nbin1_chanest + 1) / chanest_symreps;

        nbin1_default = Math.round((float)f_range[0] / inc);
        nbin2_default = Math.round((float) f_range[1] / inc) - 1;
        subcarrier_number_default = (nbin2_default - nbin1_default + 1);

        double[] preamble = PreambleGen.preamble_d();
        blocklen = preamble.length + ChirpGap + (sym_len) * Nsyms;

        f_seq = new LinkedList<>();
        for (int i = 0; i < Ns; i++) {
            f_seq.add(inc * i);
        }

        HashSet<Integer> null_carrier_set = new HashSet<>();
        for (Integer i : null_carrier) {
            null_carrier_set.add(i);
        }

        // calculate valid carriers for preamble
        LinkedList<Integer> valid_carrier_list_preamble = new LinkedList<>();
        for (int i = nbin1_chanest; i < nbin2_chanest; i += chanest_symreps) {
            if (!null_carrier_set.contains(i)) {
                valid_carrier_list_preamble.add(i);
            }
        }
        valid_carrier_preamble = new int[valid_carrier_list_preamble.size()];
        for (int i = 0; i < valid_carrier_list_preamble.size(); i++) {
            valid_carrier_preamble[i] = valid_carrier_list_preamble.get(i);
        }

        // calculate valid carriers for data
        LinkedList<Integer> valid_carrier_list_data = new LinkedList<>();
        for (int i = nbin1_data; i <= nbin2_data; i += data_symreps) {
            if (!null_carrier_set.contains(i)) {
                valid_carrier_list_data.add(i);
            }
        }
        valid_carrier_data = new int[valid_carrier_list_data.size()];
        for (int i = 0; i < valid_carrier_list_data.size(); i++) {
            valid_carrier_data[i] = valid_carrier_list_data.get(i);
        }

        // calculate valid carriers for default
        LinkedList<Integer> valid_carrier_list_default = new LinkedList<>();
        for (int i = nbin1_default; i <= nbin2_default; i++) {
            if (!null_carrier_set.contains(i)) {
                valid_carrier_list_default.add(i);
            }
        }
        valid_carrier_default = new int[valid_carrier_list_default.size()];
        for (int i = 0; i < valid_carrier_list_default.size(); i++) {
            valid_carrier_default[i] = valid_carrier_list_default.get(i);
        }
    }
}
