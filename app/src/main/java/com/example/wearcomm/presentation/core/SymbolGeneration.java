package com.example.wearcomm.presentation.core;

import android.util.Log;

import com.example.wearcomm.presentation.Constants;
import com.example.wearcomm.presentation.Utils;

import java.util.Arrays;
import java.util.Random;

/**
 * 一个与符号生成相关的类，主要用于生成用于传输的信号符号，可能涉及到通信系统中的信号调制和前导符号生成，实现将数据比特映射到物理信号中
 */
public class SymbolGeneration {

    /**
     * generatePreamble - 生成包含前导符号的信号。
     * @param bits short[] - 输入的数据比特，用于生成前导符号。
     * @param valid_carrier int[] - 有效载波的数组，指定可用于调制的载波。
     * @param symreps int - 每个符号的重复次数。
     * @param preamble boolean - 指示是否在信号中包含前导符号。
     * @param sigType Constants.SignalType - 要生成的信号类型。
     * @return txsig short[] - 生成的信号，包括或不包括前导符号。
     */

    public static short[] generatePreamble(short[] bits, int[] valid_carrier,
                                           int symreps, boolean preamble, Constants.SignalType sigType) {

        // 计算 numDataSyms（数据符号数量），这是基于输入比特长度和有效载波数量的
        int numDataSyms = 0;
        if (valid_carrier.length > 0) {
            numDataSyms = (int) Math.ceil((double)bits.length/valid_carrier.length);
        }

        // 计算单个符号的长度 symlen，包括重复次数、保护间隔和循环前缀。
        int symlen = (Constants.Ns+Constants.Cp)*symreps + Constants.Gi;

        // 计算信号的总长度 siglen，并为传输信号 txsig 分配内存。
        int siglen = symlen*numDataSyms;
        if (preamble) {
            siglen += ((Constants.preambleTime/1000.0)*Constants.fs)+Constants.ChirpGap;
        }
        short[] txsig = new short[siglen];

        // 如果 preamble 为真，生成并添加前导符号到信号中，并在前导符号和数据符号之间添加一个间隙
        int counter = 0; // 用于跟踪信号数组 txsig 中的当前位置。它将被用于填充信号数组
        if (preamble) { // 确定是否需要在信号的开头添加前导符号
            // 添加前导符号
            short[] preamble_sig = PreambleGen.preamble_s();
            // 使用 for 循环遍历前导符号数组 preamble_sig，并将其样本逐个复制到信号数组 txsig 中。
            // 每次将一个样本复制到 txsig 中后，counter 递增，以便指向 txsig 中的下一个位置。
            for (Short s : preamble_sig) {
                txsig[counter++] = s;
            }
            // 在前导符号之后添加一个间隙，称为“Chirp Gap”。这通常是通信协议的一部分，用于在前导符号和数据符号之间保持时间间隔。
            //  通过增加 counter 的值来跳过 txsig 中的这些间隙位置
            counter += Constants.ChirpGap;
        }

        // 将输入数据比特根据有效载波的数量分段，并在必要时对符号进行差分编码。
        short[][] bit_list = new short[numDataSyms][valid_carrier.length]; // 存储每个数据符号的比特,数组大小是numDataSyms行，每行长度等于valid_carrier.length有效载波数量
        int bit_counter = 0; // 跟踪处理到的比特位置
        for (int i = 0; i < numDataSyms; i++) { // 循环遍历每个数据符号，计算当前符号的比特范围
            int endpoint = bit_counter + valid_carrier.length-1; // 计算当前符号比特段的结束位置
            if (bit_counter + valid_carrier.length-1 > bits.length-1) {
                endpoint = bits.length-1; // 如果超过了输入比特的总长度就设置为此，以避免超出数组范围
            }

            // 将输入比特数组 bits 分段，提取出当前符号的比特 bits_seg
            short[] bits_seg = Utils.segment(bits,bit_counter,endpoint);
            if (i > 0) {
                // 差分编码
                bits_seg = transform_bits(bit_list[i-1], bits_seg);
            }
            bit_list[i] = bits_seg;

            // 将数据比特调制为 OFDM 符号
            short[] symbol = generate_helper(
                    bits_seg,
                    valid_carrier,
                    symreps,
                    sigType);
            bit_counter += valid_carrier.length;

            // 将调制后的符号添加到传输信号
            for (Short s : symbol) {
                txsig[counter++] = s;
            }
        }
        return txsig;
    }

    /**
     * 计算并返回基于有效载波的比特填充顺序。
     * <p>
     * 该方法确定了需要多少轮填充才能使用所有有效载波调制比特序列，并计算每轮填充中的比特数量。
     *
     * @param valid_carrier int[] - 有效载波的数组，指定可用于调制的载波。
     * @return out int[] - 包含比特填充顺序的数组。第一个元素表示总的填充轮数，后续元素表示每轮填充的比特数量。
     */

    public static int[] binFillOrder(int[] valid_carrier) {
        int numrounds = 0; // 用于存储所需的填充轮数，初始化为 0。

        String temp = ""; // 这个字符串将用于计算编码后的比特长度。
        for (int i = 0; i < Constants.maxbits; i++) {
            temp+="0";
        }
        // 如果启用了编码（即 Constants.CODING 为 true），则将 temp 进行编码，并将编码后的比特长度赋给 maxcodedbits
        int maxcodedbits = Constants.maxbits;
        if (Constants.CODING) {
            maxcodedbits = Utils.encode(temp, Constants.cc[0],Constants.cc[1],Constants.cc[2]).length();
        }

        short[] bits = new short[maxcodedbits]; // 这个数组将用于存储需要调制的比特数据。

        // 计算填充轮数
        int bit_counter = 0;
        if (valid_carrier.length > 0) {
            numrounds = (int) Math.ceil((double)maxcodedbits/valid_carrier.length);
        }

        int[] out = new int[numrounds+1]; // 存储结果数组
        out[0]=numrounds; // 第一个元素存储填充轮数 numrounds。

        // 循环计算每轮的比特数量
        for (int i = 0; i < numrounds; i++) {
            boolean oneMoreBin = i < bits.length % numrounds;

            int endpoint = (int) (bit_counter + Math.floor(bits.length / numrounds));
            if (!oneMoreBin) {
                endpoint -= 1;
            }

            short[] bits_seg = Utils.segment(bits, bit_counter, endpoint);

            short[] pad_bits = Utils.random_array(valid_carrier.length - bits_seg.length);
            out[i+1]=bits_seg.length;
        }
        return out;
    }

    /**
     * 生成用于传输的数据符号。
     * <p>
     * 该方法根据输入的数据比特和有效载波，生成调制后的数据符号序列，可能包含前导符号和训练符号。
     * 支持添加前导符号、训练符号、差分编码和符号插值等功能。
     *
     * @param bits short[] - 输入的数据比特，用于生成符号。
     * @param valid_carrier int[] - 有效载波数组，指定可用于调制的载波。
     * @param symreps int - 符号重复次数，用于增强信号强度。
     * @param preamble boolean - 是否在符号序列中添加前导符号。
     * @param sigType Constants.SignalType - 信号类型，定义符号的调制方式。
     * @param m_attempt int - 当前尝试的次数，可能用于控制算法的执行逻辑。
     * @return txsig short[] - 生成的调制后数据符号序列，包括前导符号、训练符号和数据符号。
     */

    public static short[] generateDataSymbols(short[] bits, int[] valid_carrier,
                                              int symreps, boolean preamble, Constants.SignalType sigType,
                                              int m_attempt) {
        int numrounds = 0; // 存储将输入的比特数据分配到可用的载波上所需的符号轮数
        if (valid_carrier.length > 0) {
            numrounds = (int) Math.ceil((double)bits.length/valid_carrier.length); // 向上取值，确保全部被计算
        }

        int symlen = (Constants.Ns+Constants.Cp)*symreps + Constants.Gi; // 计算单个符号的长度，包括符号的实际数据部分、循环前缀、符号重复次数以及保护间隔。

        int siglen = symlen*(numrounds+1); // 代表整个信号的样本总数，涵盖了所有符号的长度（包括数据符号、前导符号、训练符号等）。这个值用于为信号数组分配足够的空间，以容纳所有要传输的符号
        if (preamble) {
            siglen += ((Constants.preambleTime/1000.0)*Constants.fs)+Constants.ChirpGap;
        }

        short[] txsig = new short[siglen]; // 初始化一个短整数数组，用于存储最终的传输信号

        // 如果前导码是真，生成并添加前导符号到 txsig 中，并在前导符号和数据符号之间插入一个间隙。
        int counter = 0;
        if (preamble) {
            // 添加前导符号
            short[] preamble_sig = PreambleGen.preamble_s();
            for (Short s : preamble_sig) {
                txsig[counter++] = s;
            }
            counter += Constants.ChirpGap;
        }

        //存储符号生成过程中的比特数据
        short[][] bit_list = new short[numrounds+1][valid_carrier.length];

        //
        short[] training_bits = Utils.segment(Constants.pn60_bits, 0, valid_carrier.length - 1);

        // 生成一个符号并将其添加到传输信号 (txsig) 中
        short[] symbol = generate_helper(
                training_bits,
                valid_carrier,
                symreps,
                sigType);
        for (Short s : symbol) {
            txsig[counter++] = s;
        }

        bit_list[0] = training_bits; // 保存训练符号

        int bit_counter = 0;

        // 逐轮处理输入的比特数据，将其分割、填充、调制成符号，并将生成的符号添加到最终的传输信号 txsig 中
        for (int i = 0; i < numrounds; i++) {
            String bitsWithPadding = "";
            String numberOfDataBits = "";
            String bitsWithoutPadding = "";
            boolean oneMoreBin = i < bits.length%numrounds;

            int endpoint = (int)(bit_counter + Math.floor(bits.length/numrounds));
            if (!oneMoreBin) {
                endpoint -= 1;
            }

            short[] bits_seg = Utils.segment(bits,bit_counter,endpoint);
            numberOfDataBits += bits_seg.length+", ";

            bitsWithoutPadding += Utils.trim(Arrays.toString(bits_seg))+", ";

            short[] pad_bits = Utils.random_array(valid_carrier.length-bits_seg.length);

            short[] tx_bits = Utils.concat_short(bits_seg,pad_bits);
            bitsWithPadding += Utils.trim(Arrays.toString(tx_bits))+", ";

            if (Constants.INTERLEAVE) {
                shuffleArray(tx_bits, i);
            }

            if (Constants.DIFFERENTIAL) {
                tx_bits = transform_bits(bit_list[i], tx_bits);
                System.arraycopy(tx_bits, 0, bit_list[i + 1], 0, tx_bits.length);
            }
            // 将已经经过处理的比特 tx_bits 调制成符号，并将这些符号样本添加到最终的传输信号数组 txsig 中。
            symbol = generate_helper(
                    tx_bits,
                    valid_carrier,
                    symreps,
                    sigType);
            bit_counter += bits_seg.length;

            for (Short s : symbol) {
                txsig[counter++] = s;
            }
        }

        return txsig;
    }

    static void shuffleArray(short[] ar, int seed) {
        Random rnd = new Random(seed);
        for (int i = ar.length - 1; i > 0; i--) {
            int index = rnd.nextInt(i + 1);
            short a = ar[index];
            ar[index] = ar[i];
            ar[i] = a;
        }
    }

    static short[] unshuffle(short[] ar, int seed) {
        short[] tempArray = new short[ar.length];
        for (int i = 0; i < tempArray.length; i++) {
            tempArray[i] = (short)i;
        }
        shuffleArray(tempArray,seed);

        short[] out = new short[ar.length];
        for (int i = 0; i < ar.length; i++) {
            int index = tempArray[i];
            out[index] = ar[i];
        }
        return out;
    }

    public static short[] getTrainingSymbol(int[] valid_carrier) {
        short[] training_bits = Utils.segment(Constants.pn60_bits, 0, valid_carrier.length - 1);
        return generate_helper(
                training_bits,
                valid_carrier,
                1,
                Constants.SignalType.DataAdapt);
    }

    public static short[] transform_bits(short[] last_bits, short[] bits) {
        short[] newbits= new short[bits.length];
        for (int i = 0; i < bits.length; i++) {
            if (last_bits[i] != bits[i]) {
                newbits[i] = 1;
            }
        }
        return newbits;
    }

    public static short[] mod(int bound1, int bound2, int[] valid_carrier, short[] bits, int subnum, Constants.SignalType sigType) {
        double[][] mod = Modulation.pskmod(bits);
        if (bits.length < subnum) {
            bound2 = bound1+bits.length-1;
        }

        double[][] sig = new double[2][Constants.Ns];
        int counter=0;
        for (int i = bound1; i <= bound2; i++) {
            if (contains(valid_carrier, i)) {
                sig[0][i] = mod[0][counter];
                sig[1][i] = mod[1][counter++];
            }
        }

        double[][] symbol_complex = Utils.ifftnative2(sig);

        short[] symbol = new short[symbol_complex[0].length];
        double divval=(bound2-bound1)+1;
        if (sigType.equals(Constants.SignalType.Sounding)) {
            divval = divval/2;
        }
        for (int i = 0; i < symbol.length; i++) {
            symbol[i] = (short)((symbol_complex[0][i]/ divval)*32767.0);
        }

        return symbol;
    }

    // generate one symbol
    public static short[] generate_helper(short[] bits, int[] valid_carrier, int symreps, Constants.SignalType sigType) {
        int bound1=0;
        int bound2=0;
        int subnum=0;
        if (sigType.equals(Constants.SignalType.Sounding)) {
            bound1 = Constants.nbin1_default;
            bound2 = Constants.nbin2_default;
            subnum=Constants.subcarrier_number_chanest;
        }
        else if (sigType.equals(Constants.SignalType.DataAdapt)||
                sigType.equals(Constants.SignalType.DataFull_1000_4000)||
                sigType.equals(Constants.SignalType.DataFull_1000_2500)||
                sigType.equals(Constants.SignalType.DataFull_1000_1500)) {
            bound1 = valid_carrier[0];
            bound2 = valid_carrier[valid_carrier.length-1];
            subnum = bound2-bound1+1;
        }

        short[] symbol = mod(bound1, bound2, valid_carrier, bits, subnum, sigType);
        short[] flipped_symbol=null;
        if(Constants.FLIP_SYMBOL) {flipped_symbol = mod(bound1, bound2, valid_carrier, Utils.flip(bits), subnum, sigType);}

        int datacounter = 0;
        short[] out = null;

        if (sigType.equals(Constants.SignalType.DataAdapt)||
                sigType.equals(Constants.SignalType.DataFull_1000_4000)||
                sigType.equals(Constants.SignalType.DataFull_1000_2500)||
                sigType.equals(Constants.SignalType.DataFull_1000_1500)) {
            int long_cp = Constants.Cp * symreps;
            short[] cp = new short[long_cp];
            System.arraycopy(symbol, (symbol.length - long_cp - 1) + 0, cp, 0, long_cp);

            out = new short[symbol.length*symreps + long_cp + Constants.Gi];

            for (short value : cp) {
                out[datacounter++] = value;
            }
            for (int i = 0; i < symreps; i++) {
                for (short value : symbol) {
                    out[datacounter++] = value;
                }
            }
        }
        else if (sigType.equals(Constants.SignalType.Sounding)) {
            int long_cp = Constants.Cp;
            short[] cp = new short[long_cp];
            System.arraycopy(symbol, (symbol.length - long_cp - 1) + 0, cp, 0, long_cp);

            out = new short[(symbol.length+long_cp)*symreps + Constants.Gi];

            for (int i = 0; i < symreps; i++) {
                for (short value : cp) {
                    out[datacounter++] = value;
                }
                for (short value : symbol) {
                    out[datacounter++] = value;
                }
            }
        }

        return out;
    }

    public static boolean contains(int[] data, int k) {
        for (Integer i : data) {
            if (k==i) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取编码后的比特序列。
     * <p>
     * 该方法首先将int类型数据转换为二进制字符串再进行补零（int是0-31的数据则需要）
     * 如果 `Constants.CODING` 为真，
     * 则对该二进制字符串进行编码；否则，直接使用未编码的字符串。最后，将结果转换为 `short[]` 并返回。
     *
     * @return out short[] - 返回编码后的比特序列，如果没有编码则返回原始的比特序列。
     */

    public static short[] getCodedBits(int messageId) {
        String uncoded = Utils.pad2(Integer.toBinaryString(messageId));
        String coded;
        if (Constants.CODING) {
            // 涉及卷积编码，返回二进制字符串(编码后)
            coded = Utils.encode(uncoded, Constants.cc[0],Constants.cc[1],Constants.cc[2]);
        }
        else {
            coded = uncoded;
        }
        Utils.log(uncoded +"=>"+coded+"=>"+Constants.mmap.get(messageId));
        return Utils.convert(coded); // 将二进制的字符串转换为short类型的数组
    }
}
