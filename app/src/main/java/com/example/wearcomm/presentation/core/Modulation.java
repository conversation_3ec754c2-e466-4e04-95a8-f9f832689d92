package com.example.wearcomm.presentation.core;

import com.example.wearcomm.presentation.Utils;

public class Modulation {
    /**
     * the demodulation part **/
    public static double[][] pskmod(short[] bits) {
        int bit_num = bits.length;
        double[][] mod_dat = new double[2][bit_num];
        for(int i = 0; i < bit_num; ++i){
            // real
            if(bits[i] == 0) {
                mod_dat[0][i] = 1; // real
            }
            else {
                mod_dat[0][i] = -1; // real
            }
            mod_dat[1][i] = 0; // real
        }
        return mod_dat;
    }

    public static double[] phase(double[][] vals) {
        double[] out = new double[vals[0].length];
        for (int i = 0; i < out.length; i++) {
            out[i] = Math.atan2(vals[1][i], vals[0][i]);
        }
        return out;
    }

    // number of symbols / real/imaginary / data bits
    public static short[][] pskdemod_differential(double[][][] symbols, int[] valid_bins) {
        int numbins = valid_bins[1]-valid_bins[0]+1;
        // num symbols / num bins
        short[][] bits = new short[symbols.length-1][numbins];
        for (int i = 0; i < symbols.length-1; i++) {
            double[][] symbol1 = symbols[i+1];
            double[][] symbol2 = symbols[i];
            double[][] divval = Utils.dividenative(symbol1,symbol2);
            double[] phase = phase(divval);

            int counter = 0;
            for (int j = valid_bins[0]; j < valid_bins[1]; j++) {
                boolean b1 = phase[j] >= Math.PI/2;
                boolean b2 = phase[j] <= -Math.PI/2;
                if (b1|b2) {
                    bits[i][counter] = 1;
                }
                counter+=1;
            }
        }
        return bits;
    }
}
