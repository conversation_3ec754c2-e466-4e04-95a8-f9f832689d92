package com.example.wearcomm.presentation.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.util.AttributeSet;
import android.view.View;
import java.util.Random;

/**
 * 自定义波浪动画视图
 * 用于显示录音时的动态波浪效果
 */
public class WaveView extends View {
    
    private Paint wavePaint;
    private Path wavePath;
    private Random random;
    
    private int waveCount = 5; // 波浪条数
    private float[] waveHeights; // 每个波浪条的高度
    private float maxWaveHeight; // 最大波浪高度
    private float waveWidth; // 每个波浪条的宽度
    private float waveSpacing; // 波浪条之间的间距
    
    private boolean isAnimating = false;
    private Runnable animationRunnable;
    private long animationStartTime = 0;
    private static final long ANIMATION_DURATION = 5000; // 5秒动画持续时间
    private OnAnimationStoppedListener onAnimationStoppedListener;
    
    public WaveView(Context context) {
        super(context);
        init();
    }
    
    public WaveView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }
    
    public WaveView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }
    
    private void init() {
        wavePaint = new Paint();
        wavePaint.setColor(0xFF3F51B5); // 与应用主题色匹配的蓝色波浪
        wavePaint.setStyle(Paint.Style.FILL);
        wavePaint.setAntiAlias(true);
        
        wavePath = new Path();
        random = new Random();
        waveHeights = new float[waveCount];
        
        // 初始化波浪高度
        for (int i = 0; i < waveCount; i++) {
            waveHeights[i] = 0.2f; // 初始高度比例
        }
        
        // 创建动画Runnable
        animationRunnable = new Runnable() {
            @Override
            public void run() {
                if (isAnimating) {
                    // 检查动画是否已经持续了5秒
                    long currentTime = System.currentTimeMillis();
                    if (currentTime - animationStartTime >= ANIMATION_DURATION) {
                        // 5秒后自动停止动画
                        android.util.Log.d("WaveView", "动画达到5秒，准备自动停止");
                        stopAnimation();
                        // 通知监听器动画已停止
                        if (onAnimationStoppedListener != null) {
                            android.util.Log.d("WaveView", "通知监听器动画已停止");
                            onAnimationStoppedListener.onAnimationStopped();
                        }
                        return;
                    }

                    updateWaveHeights();
                    invalidate();
                    postDelayed(this, 100); // 每100ms更新一次
                }
            }
        };
    }
    
    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        maxWaveHeight = h * 0.8f; // 最大高度为视图高度的80%
        waveWidth = w / (waveCount * 2.0f); // 波浪条宽度
        waveSpacing = waveWidth * 0.5f; // 间距为宽度的一半
    }
    
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        
        if (getWidth() == 0 || getHeight() == 0) {
            return;
        }
        
        float centerY = getHeight() / 2.0f;
        float startX = (getWidth() - (waveCount * waveWidth + (waveCount - 1) * waveSpacing)) / 2.0f;
        
        for (int i = 0; i < waveCount; i++) {
            float x = startX + i * (waveWidth + waveSpacing);
            float waveHeight = waveHeights[i] * maxWaveHeight;
            
            // 绘制圆角矩形波浪条
            float top = centerY - waveHeight / 2;
            float bottom = centerY + waveHeight / 2;
            
            wavePath.reset();
            wavePath.addRoundRect(x, top, x + waveWidth, bottom, waveWidth / 2, waveWidth / 2, Path.Direction.CW);
            canvas.drawPath(wavePath, wavePaint);
        }
    }
    
    /**
     * 更新波浪高度，创建动态效果
     */
    private void updateWaveHeights() {
        for (int i = 0; i < waveCount; i++) {
            // 随机生成新的高度，范围在0.2到1.0之间
            float targetHeight = 0.2f + random.nextFloat() * 0.8f;
            
            // 平滑过渡到新高度
            waveHeights[i] = waveHeights[i] * 0.7f + targetHeight * 0.3f;
        }
    }
    
    /**
     * 开始波浪动画
     */
    public void startAnimation() {
        if (!isAnimating) {
            isAnimating = true;
            animationStartTime = System.currentTimeMillis(); // 记录动画开始时间
            post(animationRunnable);
        }
    }
    
    /**
     * 停止波浪动画
     */
    public void stopAnimation() {
        android.util.Log.d("WaveView", "stopAnimation被调用");
        isAnimating = false;
        removeCallbacks(animationRunnable);

        // 重置波浪高度到最小值
        for (int i = 0; i < waveCount; i++) {
            waveHeights[i] = 0.2f;
        }
        invalidate();
        android.util.Log.d("WaveView", "动画已停止，isAnimating=" + isAnimating);
    }
    
    /**
     * 设置波浪颜色
     */
    public void setWaveColor(int color) {
        wavePaint.setColor(color);
        invalidate();
    }
    
    /**
     * 检查是否正在动画
     */
    public boolean isAnimating() {
        return isAnimating;
    }

    /**
     * 设置动画停止监听器
     */
    public void setOnAnimationStoppedListener(OnAnimationStoppedListener listener) {
        this.onAnimationStoppedListener = listener;
    }

    /**
     * 动画停止监听器接口
     */
    public interface OnAnimationStoppedListener {
        void onAnimationStopped();
    }
}
