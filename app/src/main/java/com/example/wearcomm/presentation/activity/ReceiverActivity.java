package com.example.wearcomm.presentation.activity;

import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.ComponentActivity;
import androidx.annotation.Nullable;

import com.example.wearcomm.R;
import com.example.wearcomm.presentation.Constants;
import com.example.wearcomm.presentation.manager.ReceiverManager;
import com.example.wearcomm.presentation.manager.TaskStateManager;
import com.example.wearcomm.presentation.view.WaveView;

public class ReceiverActivity extends ComponentActivity implements ReceiverManager.OnReceiverListener {
    private static final String TAG = "ReceiverActivity";

    // 显示模式枚举
    private enum DisplayMode {
        ALICE_ONLY("Alice模式"),
        BOB_ONLY("Bob模式"),
        ALICE_AND_BOB("完整模式");

        private final String description;

        DisplayMode(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
    private LinearLayout messageContainer;
    private ScrollView scrollView;
    private ImageButton btnMicrophone;
    private WaveView waveView;
    private ReceiverManager receiverManager;
    private TaskStateManager taskStateManager;
    private Handler delayHandler;
    private Runnable startReceiverRunnable;
    private boolean isReturningToMain = false; // 标志位：是否返回到角色选择界面
    private boolean isRecording = false; // 录音状态
    private boolean isTaskCompleted = false; // 标志位：任务是否已完成
    private DisplayMode currentDisplayMode = DisplayMode.ALICE_AND_BOB; // 当前显示模式，默认为完整模式
    private int clickCount = 0; // 麦克风点击次数
    private boolean receiverStarted = false; // 接收器是否已启动

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_receiver);

        taskStateManager = TaskStateManager.getInstance();

        // 检查是否存在任务冲突
        if (!taskStateManager.canStartTask(TaskStateManager.TaskType.RECEIVER)) {
            String errorMessage = "接收任务启动失败！\n\n" +
                                "当前状态: " + taskStateManager.getStatusDescription() + "\n\n" +
                                "发送和接收任务不能同时运行。\n" +
                                "程序将退出以避免冲突。";

            Log.e(TAG, "检测到任务冲突，无法启动接收任务");
            showErrorAndExit(errorMessage);
            return;
        }

        initView();
        startReceiver();
    }

    private void initView() {
        messageContainer = findViewById(R.id.messageContainer);
        scrollView = findViewById(R.id.scrollView);
        btnMicrophone = findViewById(R.id.btnMicrophone);
        waveView = findViewById(R.id.waveView);

        // 设置麦克风按钮点击事件 - 切换显示模式
        btnMicrophone.setOnClickListener(v -> switchDisplayMode());

        // 设置波浪动画停止监听器
        waveView.setOnAnimationStoppedListener(() -> {
            Log.d(TAG, "收到动画停止回调");
            // 确保在UI线程中更新界面
            runOnUiThread(() -> {
                Log.d(TAG, "开始恢复UI状态");
                // 动画自动停止后，恢复到麦克风按钮状态
                isRecording = false;
                waveView.setVisibility(View.GONE);
                btnMicrophone.setVisibility(View.VISIBLE);
                Log.d(TAG, "UI状态已恢复: waveView=GONE, btnMicrophone=VISIBLE, isRecording=" + isRecording);
            });
        });
    }

    private void startReceiver() {
        // 停止之前的接收任务（如果存在）
        stopReceiver();

        // 注册接收任务状态
        if (!taskStateManager.startTask(TaskStateManager.TaskType.RECEIVER)) {
            String errorMessage = "接收任务注册失败！\n\n" +
                                "当前状态: " + taskStateManager.getStatusDescription() + "\n\n" +
                                "发送和接收任务不能同时运行。\n" +
                                "程序将退出以避免冲突。";

            Log.e(TAG, "接收任务注册失败，存在冲突");
            showErrorAndExit(errorMessage);
            return;
        }

        // 使用Handler延迟9秒启动接收器
        Log.d(TAG, "接收器将在9秒后启动");

        delayHandler = new Handler();
        startReceiverRunnable = () -> {
            try {
                // 确保只有一个ReceiverManager实例
                if (receiverManager == null) {
                    // 标记接收器已启动，禁用模式切换
                    receiverStarted = true;

                    receiverManager = new ReceiverManager(this);
                    receiverManager.start();
                    Log.d(TAG, "接收器已启动，模式: " + currentDisplayMode.getDescription() + "，模式切换已禁用");
                }
            } catch (Exception e) {
                Log.e(TAG, "启动接收器时发生错误: " + e.getMessage());
                // 如果启动失败，注销任务状态
                taskStateManager.stopTask(TaskStateManager.TaskType.RECEIVER);
                receiverStarted = false; // 重置状态
            }
        };

        delayHandler.postDelayed(startReceiverRunnable, 9000); // 9秒延迟
    }

    private void stopReceiver() {
        // 取消延迟任务
        if (delayHandler != null && startReceiverRunnable != null) {
            delayHandler.removeCallbacks(startReceiverRunnable);
            Log.d(TAG, "已取消接收器延迟启动任务");
        }

        // 停止接收管理器
        if (receiverManager != null) {
            try {
                receiverManager.interrupt();
                receiverManager = null;
                Log.d(TAG, "接收器已停止");
            } catch (Exception e) {
                Log.e(TAG, "停止接收器时发生错误: " + e.getMessage());
            }
        }

        // 注销接收任务状态
        if (taskStateManager != null) {
            taskStateManager.stopTask(TaskStateManager.TaskType.RECEIVER);
            Log.d(TAG, "接收任务状态已注销");
        }

        // 重置接收器状态，允许重新选择模式
        receiverStarted = false;
        clickCount = 0; // 重置点击次数
        currentDisplayMode = DisplayMode.ALICE_AND_BOB; // 重置为默认模式
        Log.d(TAG, "接收器状态已重置，可重新选择模式");
    }

    /**
     * 切换显示模式
     */
    private void switchDisplayMode() {
        // 如果接收器已启动，则不允许切换模式
        if (receiverStarted) {
            Log.d(TAG, "接收器已启动，无法切换模式");
            return;
        }

        // 增加点击次数
        clickCount++;

        // 根据点击次数确定模式：单数=Alice，双数=Bob
        if (clickCount % 2 == 1) {
            // 单数次点击：Alice模式
            currentDisplayMode = DisplayMode.ALICE_ONLY;
        } else {
            // 双数次点击：Bob模式
            currentDisplayMode = DisplayMode.BOB_ONLY;
        }

        // 显示当前模式（使用Toast）
        showModeToast();

        Log.d(TAG, "点击次数: " + clickCount + ", 切换到显示模式: " + currentDisplayMode.getDescription());
    }

    /**
     * 使用Toast显示当前模式信息（0.5秒）
     */
    private void showModeToast() {
        String modeMessage = "当前模式: " + currentDisplayMode.getDescription();
        Toast toast = Toast.makeText(this, modeMessage, Toast.LENGTH_SHORT);
        toast.show();

        // 0.1秒后手动取消Toast
        new Handler().postDelayed(() -> {
            toast.cancel();
        }, 100);

        Log.d(TAG, "显示模式Toast: " + modeMessage);
    }

    /**
     * 开始录音
     */
    private void startRecording() {
        isRecording = true;

        // 隐藏麦克风按钮，显示波浪动画
        btnMicrophone.setVisibility(View.GONE);
        waveView.setVisibility(View.VISIBLE);
        waveView.startAnimation();

        Log.d(TAG, "开始录音");
    }

    /**
     * 停止录音
     */
    private void stopRecording() {
        isRecording = false;

        // 停止波浪动画，显示麦克风按钮
        waveView.stopAnimation();
        waveView.setVisibility(View.GONE);
        btnMicrophone.setVisibility(View.VISIBLE);

        Log.d(TAG, "停止录音");
    }

    @Override
    public void onReceiverMessage(String message) {
        // 根据当前显示模式过滤消息
        if (!shouldDisplayMessage(message)) {
            Log.d(TAG, "根据当前模式过滤消息: " + message);
            return;
        }

        // 在UI线程添加新的消息项
        runOnUiThread(() -> {
            if (messageContainer != null) {
                // 创建新的消息视图
                LayoutInflater inflater = LayoutInflater.from(this);
                View messageView = inflater.inflate(R.layout.message_received_item, messageContainer, false);
                TextView tvMessageText = messageView.findViewById(R.id.tvMessageText);
                tvMessageText.setText(message);

                // 添加到容器中
                messageContainer.addView(messageView);

                // 自动滚动到底部
                scrollView.post(() -> scrollView.fullScroll(View.FOCUS_DOWN));

                Log.d(TAG, "显示消息: " + message);
            }
        });
    }

    /**
     * 根据当前显示模式判断是否应该显示该消息
     */
    private boolean shouldDisplayMessage(String message) {
        if (message == null) {
            return false;
        }

        switch (currentDisplayMode) {
            case ALICE_ONLY:
                return message.startsWith("Alice:");
            case BOB_ONLY:
                return message.startsWith("Bob:");
            case ALICE_AND_BOB:
                return true; // 显示所有消息
            default:
                return true;
        }
    }

    @Override
    public void onReceiverCompleted() {
        // 接收任务完成，标记任务已完成
        Log.d(TAG, "接收任务已完成，清理任务状态");
        isTaskCompleted = true;

        // 在UI线程中执行状态清理
        runOnUiThread(() -> {
            // 注销接收任务状态
            if (taskStateManager != null) {
                taskStateManager.stopTask(TaskStateManager.TaskType.RECEIVER);
                Log.d(TAG, "接收任务状态已注销（任务完成）");
            }

            // 清理ReceiverManager引用
            receiverManager = null;

            // 重置接收器状态，允许重新选择模式
            receiverStarted = false;
            clickCount = 0; // 重置点击次数
            currentDisplayMode = DisplayMode.ALICE_AND_BOB; // 重置为默认模式
            Log.d(TAG, "接收任务完成，状态已重置，可重新选择模式");
        });
    }

    @Override
    protected void onResume() {
        super.onResume();

        // 重置返回标志位
        isReturningToMain = false;

        // 检查任务状态并更新UI
        updateUIFromBackgroundState();

        Log.d(TAG, "从后台返回，已更新UI状态");
    }

    @Override
    protected void onPause() {
        super.onPause();

        // 检查Activity是否正在结束（包括按返回键、滑动返回等所有情况）
        if (isFinishing()) {
            // Activity正在结束，说明用户要返回主界面
            isReturningToMain = true;
            Log.d(TAG, "检测到Activity正在结束，标记为返回主界面");

            // 立即停止接收任务
            stopReceiver();
        } else {
            // Activity只是暂停（如Home键），断开UI连接但保持任务运行
            if (receiverManager != null) {
                receiverManager.disconnectListener();
                Log.d(TAG, "暂停时断开接收管理器与UI的连接");
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        if (isReturningToMain) {
            // 返回到角色选择界面：停止所有任务并释放资源
            Log.d(TAG, "返回到角色选择界面，停止所有接收任务");
            stopReceiver();
        } else if (isTaskCompleted) {
            // 任务已完成：无需额外处理，资源已在onReceiverCompleted中清理
            Log.d(TAG, "任务已完成，资源已清理");
        } else {
            // 其他所有退出场景：按退出到系统处理，任务继续后台运行
            Log.d(TAG, "退出到系统，任务继续后台运行");

            // 取消延迟任务，但不中断已经运行的接收任务
            if (delayHandler != null && startReceiverRunnable != null) {
                delayHandler.removeCallbacks(startReceiverRunnable);
                Log.d(TAG, "已取消接收器延迟启动任务");
            }

            // 断开ReceiverManager与UI的连接，避免内存泄漏
            if (receiverManager != null) {
                receiverManager.disconnectListener();
                Log.d(TAG, "断开接收管理器与UI的连接");
            }

            // 不中断接收任务，让其继续在后台运行
            // 注意：不注销任务状态，因为任务继续在后台运行
            Log.d(TAG, "接收任务继续后台运行");
        }
    }

    /**
     * 从后台状态更新UI
     */
    private void updateUIFromBackgroundState() {
        // 检查任务状态管理器中的接收任务状态
        if (taskStateManager.getCurrentRunningTask() == TaskStateManager.TaskType.RECEIVER) {
            Log.d(TAG, "检测到接收任务正在后台运行，重新连接UI");

            // 如果接收任务正在运行，重新建立ReceiverManager与UI的连接
            if (receiverManager != null && !isTaskCompleted) {
                // 重新连接监听器，恢复UI更新
                receiverManager.reconnectListener(this);
                Log.d(TAG, "重新建立接收管理器与UI的连接");

                // 更新UI状态
                runOnUiThread(() -> {
                    // 可以在这里添加"接收中..."的状态显示
                    Log.d(TAG, "UI已更新以反映后台接收任务状态");
                });
            }
        } else {
            Log.d(TAG, "没有检测到后台接收任务");
        }
    }

    /**
     * 显示错误消息并直接退出程序
     */
    private void showErrorAndExit(String errorMessage) {
        Log.e(TAG, "任务冲突错误: " + errorMessage);

        // 强制停止所有任务
        taskStateManager.forceStopAllTasks();

        // 显示Toast错误消息
        android.widget.Toast.makeText(this, "程序退出",
                                     android.widget.Toast.LENGTH_LONG).show();

        // 直接退出程序
        System.exit(1);
    }

    @Override
    public void onBackPressed() {
        // 用户按返回键，返回到角色选择界面
        // 注意：实际的停止逻辑在onPause()中处理，这里只需要调用super
        Log.d(TAG, "用户按下返回键，返回到角色选择界面");
        super.onBackPressed();
    }
}
