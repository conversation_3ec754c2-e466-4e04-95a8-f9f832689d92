package com.example.wearcomm.presentation.core;

import com.example.wearcomm.presentation.Constants;
import com.example.wearcomm.presentation.Utils;

public class PreambleGen {
    /**
     * 生成并返回一个用于探测信道的前导音频信号。
     * <p>
     * 该方法调用 `SymbolGeneration.generatePreamble` 方法生成一个前导信号，该信号用于信道探测（Sounding）。
     * 前导信号包含特定的比特序列、载波、重复次数等参数，以确保在数据传输之前进行信道估计或同步操作。
     *
     * @return short[] - 生成的用于探测信道的短整型音频信号数组。
     */
    public static short[] sounding_signal_s() {
        return SymbolGeneration.generatePreamble(Constants.pn60_bits, Constants.valid_carrier_data,
                Constants.chanest_symreps, true, Constants.SignalType.Sounding); } //pn60_bits是代表了一个伪随机序列，长度是60bit.

    public static short[] preamble_s() {
        return Utils.convert_s(preamble_d());
    }

    /**
     * 返回用于数据传输的前导信号。
     * <p>
     * 该方法返回一个预定义的双精度浮点数数组 `Constants.naiser`，作为前导信号用于数据传输。前导信号通常用于通信系统中的同步、信道估计或帧检测。
     *
     * @return double[] - 预定义的前导信号数组。
     */
    public static double[] preamble_d() { return (Constants.naiser); }
}
