package com.example.wearcomm.presentation.core;

import android.util.Log;

import com.example.wearcomm.presentation.utils.ContextUtils;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.Objects;
import java.util.Scanner;

public class FileOperations {
    public static double[] readrawasset(int id, int normalizer) {
        Scanner            inp = new Scanner(ContextUtils.application.getResources().openRawResource(id));
        LinkedList<Double> ll  = new LinkedList<>();
        while (inp.hasNextLine()) {
            ll.add((Double.parseDouble(inp.nextLine()) / normalizer));
        }
        inp.close();
        double[] ar      = new double[ll.size()];
        int      counter = 0;
        for (Double d : ll) {
            ar[counter++] = d;
        }
        ll.clear();

        return ar;
    }

    public static short[] readrawasset_binary( int id) {
        InputStream        inp      = ContextUtils.application.getResources().openRawResource(id);
        ArrayList<Integer> ll       = new ArrayList<>();
        int                counter  = 0;
        int                byteRead;
        try {
            while ((byteRead = inp.read()) != -1) {
                ll.add(byteRead);
                counter += 1;
            }
            inp.close();
        } catch (Exception e) {
            Log.e("asdf", Objects.requireNonNull(e.getMessage()));
        }
        short[] ar = new short[ll.size() / 2];

        counter = 0;
        for (int i = 0; i < ll.size(); i += 2) {
            int out = ll.get(i) + ll.get(i + 1) * 256;
            if (out > 32767) {
                out = out - 65536;
            }
            ar[counter++] = (short) out;
        }

        return ar;
    }
}
