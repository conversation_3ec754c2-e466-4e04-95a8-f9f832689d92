package com.example.wearcomm.presentation.core;

import android.util.Log;
import com.example.wearcomm.presentation.Constants;
import com.example.wearcomm.presentation.Utils;

public class ChannelEstimate {
    public static int[] extractSignal_withsymbol_helper( double[] rec, int start_point) {
        int rx_preamble_start = start_point;
        rx_preamble_start+=240;

        int rx_preamble_end = rx_preamble_start + (int) (((Constants.preambleTime / 1000.0) * Constants.fs)) - 1;

        if (rx_preamble_end - 1 > rec.length || rx_preamble_start < 0) {
            Utils.log("Error extracting preamble from sounding signal " + rx_preamble_start + "," + rx_preamble_end);
            return new int[]{};
        }

        int rx_sym_start = rx_preamble_end + Constants.ChirpGap + 1;
        int rx_sym_end = rx_sym_start + ((Constants.Ns + Constants.Cp)* Constants.chanest_symreps) - 1;

        if (rx_sym_end - 1 > rec.length || rx_sym_start < 0) {
            Utils.log("Error extracting preamble from sounding signal");
            return new int[]{};
        }

        double[] rx_symbols = Utils.segment(rec, rx_sym_start, rx_sym_end);
        rx_symbols = Utils.div(rx_symbols,30000);


        int freqSpacing = Constants.fs/Constants.Ns;
        int[] fseq = Utils.linspace(Constants.f_range[0],freqSpacing,Constants.f_range[1]);

        double[] snrs;
        int thresh;
        int cc=Constants.Cp;
        double [][][] spec_est = new double[2][Constants.subcarrier_number_default][Constants.chanest_symreps];
        for (int i = 0; i < Constants.chanest_symreps; i++) {
            double[] seg = Utils.segment(rx_symbols,cc,cc+Constants.Ns-1);
            double[][] spec = Utils.fftcomplexoutnative_double(seg,seg.length);

            int bin_counter=0;
            for (Integer bin : Constants.valid_carrier_default) {
                double realPart=spec[0][bin];
                double imagPart=spec[1][bin];
                spec_est[0][bin_counter][i] = realPart;
                spec_est[1][bin_counter++][i] = imagPart;
            }
            cc+=Constants.Ns+Constants.Cp;
        }

        snrs = SNR_freq.calculate_snr(spec_est, Constants.pn60_syms, 1, Constants.chanest_symreps);

        thresh=Constants.SNR_THRESH2;

        int[] freqs;
        int[] selected;
        selected = Fre_adaptation.select_fre_bins(snrs, thresh);

        if (selected.length==2&&selected[0] != -1 && selected[1] != -1) {
            if (selected[1]-selected[0]==1) {
                if (selected[0] > 0) {
                    selected[0] -= 1;
                }
                else {
                    selected[1] += 1;
                }
            }
            freqs = new int[selected.length];
            for (int i = 0; i < selected.length; i++) {
                freqs[i] = fseq[selected[i]];
            }
        }

        return selected;
    }
}
