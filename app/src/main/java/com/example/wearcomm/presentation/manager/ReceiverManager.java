package com.example.wearcomm.presentation.manager;

import com.example.wearcomm.presentation.AudioSpeaker;
import com.example.wearcomm.presentation.Constants;
import com.example.wearcomm.presentation.utils.LogUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Random;

public class ReceiverManager extends Thread {
    private volatile OnReceiverListener onReceiverListener;
    private static final long DISPLAY_INTERVAL_MS = 1500; // 每次显示间隔1.5秒

    // 消息组内部类
    private static class MessageGroup {
        int startId;        // 起始ID
        int messageCount;   // 要显示的消息数量
        String groupName;   // 组名

        MessageGroup(int startId, int messageCount, String groupName) {
            this.startId = startId;
            this.messageCount = messageCount;
            this.groupName = groupName;
        }
    }

    public ReceiverManager(OnReceiverListener onReceiverListener) {
        this.onReceiverListener = onReceiverListener;
    }

    /**
     * 断开与UI的连接，避免内存泄漏
     */
    public void disconnectListener() {
        this.onReceiverListener = null;
        LogUtils.e("ReceiverManager已断开与UI的连接");
    }

    /**
     * 重新连接UI监听器
     */
    public void reconnectListener(OnReceiverListener listener) {
        this.onReceiverListener = listener;
        LogUtils.e("ReceiverManager已重新连接与UI的连接");
    }



    public void fire() {
        try {
            // 检查是否被中断
            if (Thread.currentThread().isInterrupted()) {
                LogUtils.e("接收器任务在开始前被中断");
                return;
            }

            // 创建消息组列表
            List<MessageGroup> messageGroups = createMessageGroups();

            // 按照消息数量从小到大排序
            Collections.sort(messageGroups, new Comparator<MessageGroup>() {
                @Override
                public int compare(MessageGroup g1, MessageGroup g2) {
                    return Integer.compare(g1.messageCount, g2.messageCount);
                }
            });

            // 依次显示每个消息组
            for (int groupIndex = 0; groupIndex < messageGroups.size(); groupIndex++) {
                MessageGroup group = messageGroups.get(groupIndex);

                // 检查是否被中断
                if (Thread.currentThread().isInterrupted()) {
                    LogUtils.e("接收器任务被中断");
                    return;
                }

                LogUtils.e("开始 " + group.groupName + " 组消息");

                // 显示该组的消息
                for (int i = 0; i < group.messageCount; i++) {
                    // 检查是否被中断
                    if (Thread.currentThread().isInterrupted()) {
                        LogUtils.e("接收器任务在显示消息期间被中断");
                        return;
                    }

                    int messageId = group.startId + i;
                    String message = Constants.mmap.get(messageId);
                    LogUtils.e("消息ID: " + messageId + ", 内容: " + message);

                    if (message != null && onReceiverListener != null) {
                        // 只显示消息内容，不显示额外信息
                        onReceiverListener.onReceiverMessage(message);
                    }

                    // 如果不是该组的最后一个消息，等待1秒再显示下一个
                    if (i < group.messageCount - 1) {
                        if (!sleepMs(DISPLAY_INTERVAL_MS)) {
                            LogUtils.e("接收器任务被中断");
                            return;
                        }
                    }
                }

                LogUtils.e("完成 " + group.groupName + " 组消息");

                // 如果不是最后一个组，组间也等待1秒
                if (groupIndex < messageGroups.size() - 1) {
                    if (!sleepMs(DISPLAY_INTERVAL_MS)) {
                        LogUtils.e("接收器任务在等待期间被中断");
                        return;
                    }
                }
            }

            int totalMessages = 0;
            for (MessageGroup group : messageGroups) {
                totalMessages += group.messageCount;
            }
            LogUtils.e("接收任务完成，共 " + totalMessages + " 个消息");

            // 通知任务完成
            if (onReceiverListener != null) {
                onReceiverListener.onReceiverCompleted();
            }

        } catch (Exception e) {
            LogUtils.e("接收器线程发生异常: " + e.getMessage());
            if (onReceiverListener != null) {
                onReceiverListener.onReceiverMessage("接收器遇到错误: " + e.getMessage());
                // 即使发生异常，也要通知任务完成以清理状态
                onReceiverListener.onReceiverCompleted();
            }
        }
    }

    /**
     * 创建消息组列表
     * 随机决定显示哪些组以及每组的起始ID和消息数量
     */
    private List<MessageGroup> createMessageGroups() {
        List<MessageGroup> groups = new ArrayList<>();

        // 总是同时显示Alice组和Bob组，但消息次数随机

        // 创建Alice组
        int aliceMessageCount = 1 + (int)(Math.random() * 2); // 随机1或2个
        int aliceStartId;
        if (aliceMessageCount == 1) {
            // 显示1个消息：从ID 1或2中随机选择
            aliceStartId = 1 + (int)(Math.random() * 2); // 1或2
        } else {
            // 显示2个消息：从ID 1开始
            aliceStartId = 1;
        }
        groups.add(new MessageGroup(aliceStartId, aliceMessageCount, "Alice"));

        // 创建Bob组
        int bobMessageCount = 1 + (int)(Math.random() * 2); // 随机1或2个
        int bobStartId;
        if (bobMessageCount == 1) {
            // 显示1个消息：从ID 4或5中随机选择
            bobStartId = 4 + (int)(Math.random() * 2); // 4或5
        } else {
            // 显示2个消息：从ID 4开始
            bobStartId = 4;
        }
        groups.add(new MessageGroup(bobStartId, bobMessageCount, "Bob"));

        return groups;
    }

    private boolean sleepMs(long ms) {
        try {
            Thread.sleep(ms);
            return true;
        } catch (InterruptedException e) {
            LogUtils.e("接收器睡眠被中断: " + e.getMessage());
            Thread.currentThread().interrupt(); // 重新设置中断状态
            return false;
        }
    }

    @Override
    public void run() {
        try {
            fire();
        } catch (Exception e) {
            LogUtils.e("接收器运行时发生异常: " + e.getMessage());
        }
    }

    public interface OnReceiverListener {
        void onReceiverMessage(String message);
        void onReceiverCompleted(); // 新增：任务完成回调
    }
}

