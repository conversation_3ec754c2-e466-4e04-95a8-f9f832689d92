package com.example.wearcomm.presentation.core;

import com.example.wearcomm.presentation.Constants;
import com.example.wearcomm.presentation.Utils;

public class Fre_adaptation {
    /**
     * 选择满足信噪比阈值要求的频率区间。
     * <p>
     * 该方法根据输入的信噪比（SNR）数组和阈值，选择出一个连续的频率区间，使得区间内的信噪比最小值大于阈值。该方法在频率区间长度逐渐减小的过程中，找到满足要求的最佳区间。
     *
     * @param SNR       一个包含各频率信噪比的数组，表示各个频率点的信噪比值。
     * @param threshold 阈值，表示信噪比的最低要求。
     * @return 一个整数数组 fre_range，其中包含了选定的频率区间的起始索引和结束索引。
     *         如果未能找到符合要求的区间，则 fre_range 返回 [-1, -1]。
     * <p>
     * 该方法的主要步骤如下：
     * <p>
     * 1. 初始化所需的变量：
     *    - `total_bins`：表示 SNR 数组的长度，即频率点的总数。
     *    - `select_idx`：用于记录选择的频率区间的起始和结束索引，初始值为 [-1, -1]。
     *    - `fre_range`：用于存储最终选定的频率区间的索引范围。
     * <p>
     * 2. 从最大区间长度 `L = total_bins` 开始，逐渐缩小区间长度，寻找满足要求的频率区间。
     *    - `incre`：计算当前区间长度 `L` 下的增益，用于放大信噪比的评估。
     *    - 遍历可能的区间起始索引 `i`，计算当前区间的信噪比谷值 `valley`。
     *    - 如果 `valley` 小于阈值，则跳过该区间，否则更新最佳区间索引 `best_idx` 和最佳谷值 `best_valley`。
     * <p>
     * 3. 如果找到符合要求的区间，更新 `select_idx` 为最佳区间的起始和结束索引；否则，继续缩小区间长度。
     *
     * 4. 最后，如果未找到符合要求的区间，返回 [-1, -1]；否则，返回选定的频率区间。
     */
    public static int[] select_fre_bins(double[] SNR, double threshold) {
        int total_bins = SNR.length;
        int[] select_idx = new int[2];
        select_idx[0] = -1;
        select_idx[1] = -1;
        int[] fre_range = new int[2];

        for(int L = total_bins; L >0 ; L--){
            double incre = Utils.mag2db((double)(total_bins)/(double)(L))/2* Constants.FreAdaptScaleFactor;
            int best_idx = -1;
            double best_valley = -1;
            for(int i = 0; i < total_bins - L + 1 ; ++i){
                double valley = Utils.min(SNR, i, i+L) + incre;
                if(valley < threshold){
                    continue;
                }
                else {
                    if(valley > best_valley) {
                        best_idx = i;
                        best_valley = valley;
                    }
                }
            }
            if(best_idx >= 0){
                select_idx[0] = best_idx;
                select_idx[1] = best_idx + L -1;
                break;
            }
        }

        if(select_idx[0] == -1){
            fre_range[0] = -1;
            fre_range[1] = -1;
        }
        else{
            fre_range[0] = select_idx[0];
            fre_range[1] = select_idx[1];
        }

        return fre_range;
    }
}