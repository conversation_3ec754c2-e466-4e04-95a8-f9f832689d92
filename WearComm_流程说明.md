# WearComm 应用流程说明

## 应用概述

WearComm 是一个智能手表通信应用，支持发送和接收预定义消息，通过音频信号进行数据传输。应用界面标题已优化为"Sender"和"Receiver"以便区分功能。

## 主要功能

### 1. 发送者模式 (Sender)
- 发送预定义的2条消息 (ID: 1-2，Alice的消息)
- 使用音频信号传输数据
- 支持后台运行（息屏不停止，使用WakeLock保护）
- 界面标题显示"Sender"
- UI显示与信号发送同步进行

### 2. 接收者模式 (Receiver)
- 分组消息显示规则（Alice组和Bob组）
- 智能组合显示逻辑
- 按消息数量从小到大排列显示
- 消息间隔1.5秒，自然显示节奏
- 音频反馈机制（2000-3500Hz随机频率）
- 界面标题显示"Receiver"

## 详细流程

### Sender 流程

1. **启动阶段**
   - 用户点击"Sender"按钮
   - 进入发送者界面，显示标题"Sender"
   - 获取 WakeLock 防止设备休眠

2. **等待阶段**
   - 5秒静默期等待
   - 分成小段等待，支持中断响应

3. **发送阶段**
   - 依次发送消息 ID: 1, 2（Alice的消息）
   - 消息内容：
     - ID 1: "Alice: Where are you?"
     - ID 2: "Alice: Tell me your coordinate"
   - UI显示与信号发送同步进行
   - 消息间隔 1 秒
   - 界面实时显示发送内容

4. **完成阶段**
   - 所有2条消息发送完毕
   - 释放 WakeLock
   - 任务自动结束

### Receiver 流程

1. **启动阶段**
   - 用户点击"Receiver"按钮
   - 进入接收者界面，显示标题"Receiver"
   - 设置 10 秒延迟启动

2. **准备阶段**
   - 8 秒倒计时等待
   - **随机决定显示哪些消息组（Alice组、Bob组，或两组都显示）**
   - **为每个要显示的组随机选择起始ID和消息数量**

3. **接收阶段**
   - **分组显示规则**：
     - **Alice组 (ID 1-3)**：
       - 起始ID=1：随机显示2-3个消息 (1,2 或 1,2,3)
       - 起始ID=2：随机显示1-2个消息 (2 或 2,3)
     - **Bob组 (ID 4-6)**：
       - 起始ID=4：随机显示2-3个消息 (4,5 或 4,5,6)
       - 起始ID=5：随机显示1-2个消息 (5 或 5,6)
   - **显示顺序**：按消息数量从小到大排列各组
   - 每条消息间隔 1.5 秒，组间也间隔 1.5 秒
   - 界面实时显示接收内容
   - 每条消息播放音频反馈信号 (2000-3500Hz 随机频率)

4. **完成阶段**
   - 显示完所有组的消息
   - 任务自动结束
   - **注意：每次运行都是独立任务，随机生成新的组合**

## 生命周期管理

### 智能退出检测机制（手表适配）

#### 任务停止的场景
- **滑动返回主界面**: 手表的主要返回方式，立即停止所有任务
- **检测机制**: 使用 `isFinishing()` 在 `onPause()` 中检测Activity是否正在结束
- **处理行为**: 立即中断任务、停止音频反馈、释放资源、注销任务状态

#### 任务继续运行的场景
- **按HOME键退出**: 任务转为后台运行，断开UI连接
- **息屏**: 任务继续运行（发送端有 WakeLock 保护，接收端正常运行）
- **任务切换**: 切换到其他应用时任务继续运行
- **设备休眠**: 发送端通过 WakeLock 保持运行
- **系统回收Activity**: 任务继续运行，自动断开UI连接避免内存泄漏

#### 后台返回UI恢复
- **触发条件**: 从后台重新进入应用
- **检测机制**: `onResume()` 中检查任务状态
- **处理行为**: 重新建立UI连接，恢复消息更新，更新UI状态

### 核心设计原则
**手表交互适配的二分法逻辑：**
- ✅ **滑动返回主界面** → 停止任务，释放所有资源
- ✅ **HOME键退出到系统** → 任务继续运行，断开UI连接

**具体处理策略：**
- **滑动返回主界面时**（`isFinishing() = true`）：
  - 立即停止发送/接收任务
  - 停止音频反馈信号
  - 释放WakeLock
  - 注销任务状态
  - 清理所有资源
- **HOME键退出到系统时**（`isFinishing() = false`）：
  - 保持任务在后台运行
  - 断开Manager与Activity的UI连接
  - 保持WakeLock（发送端）
  - 保持任务状态注册
- **从后台返回时**：
  - 检测后台运行的任务
  - 重新建立UI连接（`reconnectListener()`）
  - 恢复实时消息显示

### 技术实现细节
- **智能退出检测**: 使用 `isFinishing()` 在 `onPause()` 中区分退出场景
- **手表交互适配**: 专为手表的滑动返回交互模式设计
- **任务完成回调**: 新增 `onSenderCompleted()` 和 `onReceiverCompleted()` 回调机制
- **状态标志管理**: 使用 `isTaskCompleted` 标志位避免重复资源清理
- **UI重连机制**: 提供 `reconnectListener()` 方法恢复后台任务的UI更新
- **循环检查替代join()**: 使用循环检查替代阻塞的 `sendManager.join()`
- **内存泄漏防护**: 自动断开Manager与Activity的listener连接
- **线程安全**: 使用 `volatile` 关键字确保listener的线程安全访问
- **完善的中断机制**: 支持优雅的任务中断和资源释放

### 用户体验
- **直观的手表交互**: 滑动返回=停止任务，HOME键=后台运行
- **误操作保护**: 误触Home键、任务切换等不会中断正在进行的通信
- **明确的退出方式**: 滑动返回能立即停止任务，用户行为预期明确
- **无缝后台运行**: 支持真正的后台运行，重新进入时UI自动恢复
- **实时状态同步**: 后台任务状态与UI状态保持同步
- **资源优化**: 智能的资源管理，避免不必要的电池消耗

## 技术特性

### 中断处理
- **完善的中断机制**: 在循环和睡眠中检查中断状态
- **优雅的资源清理**: 自动释放 WakeLock 和线程资源
- **防止内存泄漏**: 正确的生命周期管理

### 音频处理
- **发送端**: 生成音频信号传输数据，使用 SymbolGeneration 编码
- **接收端**: 播放反馈音频 (2000-3500Hz 随机频率)
- **采样率**: 根据 Constants.fs 配置
- **音频长度**: 反馈信号长度为 24000 采样点

### 界面更新
- **实时显示消息内容**: 发送和接收的消息实时显示
- **自动滚动到最新消息**: 界面自动滚动到底部
- **清晰的界面标识**: Sender/Receiver 标题区分功能
- **详细日志记录**: 完整的操作日志便于调试

### WakeLock 管理
- **发送端**: 使用 PARTIAL_WAKE_LOCK 防止设备休眠
- **自动释放**: 任务完成或中断时自动释放
- **异常处理**: 即使发生异常也能正确释放资源

## 消息映射

| ID | 消息内容 |
|----|----------|
| 1 | Alice: Where are you? |
| 2 | Alice: Tell me your coordinate |
| 3 | Alice: I need your help |
| 4 | Bob: Need assistance |
| 5 | Bob: Mission complete |
| 6 | Bob: Weak signal |
| 7 | Good Luck |
| 8 | Hello, Sir |
| 9 | Good night |
| 10 | Good Bye,See you later |

## 消息显示规则详解

### Receiver 分组显示逻辑
- **组合决定**: 每次运行随机决定显示哪些组（Alice组、Bob组，或两组都显示）
- **起始ID选择**: 每组随机选择起始ID
- **消息数量规则**:
  - 起始ID为1或4：该组显示2-3个消息
  - 起始ID为2或5：该组显示1-2个消息
- **排序规则**: 按各组消息数量从小到大排列
- **任务独立性**: 每次运行都是独立任务，随机生成新的组合

### 示例场景
- **场景1**: 只显示Alice组，起始ID=1，显示2个 → 显示消息1, 2
- **场景2**: 只显示Bob组，起始ID=5，显示1个 → 显示消息5
- **场景3**: 显示两组，Alice(ID=2,1个)，Bob(ID=4,3个) → 先显示消息2，再显示消息4,5,6
- **场景4**: 显示两组，Alice(ID=1,3个)，Bob(ID=5,2个) → 先显示消息5,6，再显示消息1,2,3

## 时间控制详解

### 发送端时间线
```
0秒: 启动SenderActivity
5秒: 启动SendManager
5秒: 显示第一个消息 + 立即发送第一个信号 (约300ms播放时间)
5.3秒: 等待1秒
6.3秒: 显示第二个消息 + 立即发送第二个信号 (约300ms播放时间)
6.6秒: 完成
```

### 接收端时间详解

**最短情况（各组1个消息）：**
```
8秒: Alice消息1
9.5秒: 组间等待1.5秒
9.5秒: Bob消息1
总时间: 1.5秒
```

**最长情况（各组2个消息）：**
```
8秒: Alice消息1
9.5秒: Alice消息2
11秒: 组间等待1.5秒
11秒: Bob消息1
12.5秒: Bob消息2
总时间: 4.5秒
```

### 接收端时间线
```
0秒: 启动ReceiverActivity
8秒: 启动ReceiverManager
8秒: 显示第一个消息
9.5秒: 显示第二个消息
11秒: 显示第三个消息（如果有）
12.5秒: 显示第四个消息（如果有）
```

### 时间优势分析
- **发送端比接收端早3秒启动任务**
- **发送端6.6秒完成，接收端8-12.5秒完成**
- **UI显示与信号发送完全同步**
- **消息间隔1.5秒，自然显示节奏**
- **时间控制精确，确保发送端始终领先**

## 注意事项

1. **设备兼容性**: 专为智能手表设计
2. **权限要求**: 需要音频录制和 WakeLock 权限
3. **电池优化**: 合理使用 WakeLock，只在返回角色选择界面时释放
4. **生命周期理解**: 只有返回键会停止任务，其他退出方式都会保持后台运行
5. **息屏保护**: 发送任务在设备息屏时继续运行
6. **资源管理**: 完善的内存泄漏防护和资源清理机制
7. **后台运行**: 支持真正的后台运行，不受系统Activity管理影响

## 最新修复

- ✅ **手表交互适配**: 专为手表滑动返回交互模式设计
- ✅ **智能退出检测**: 使用 `isFinishing()` 区分滑动返回和HOME键退出
- ✅ **任务生命周期完善**: 立即停止机制和任务完成状态管理
- ✅ **后台UI重连**: 从后台返回时自动恢复消息更新
- ✅ **界面标题优化**: Sender/Receiver 标题区分
- ✅ **后台运行保护**: HOME键退出时任务转为后台运行
- ✅ **内存泄漏防护**: 自动断开Manager与Activity的连接
- ✅ **发送端优化**: 只发送Alice的2条消息（ID 1-2）
- ✅ **接收端分组逻辑**: 实现Alice组和Bob组的智能分组显示
- ✅ **消息排序规则**: 按消息数量从小到大排列各组
- ✅ **时间优化**: 发送端5秒启动，接收端8秒启动，确保发送端领先3秒
- ✅ **同步发送**: UI显示与信号发送同步进行，消息间隔1秒
- ✅ **自然显示**: 接收端消息间隔1.5秒，提供自然的阅读节奏
- ✅ **中断机制完善**: 支持优雅的任务中断和资源清理
- ✅ **WakeLock 保护**: 发送端息屏时继续运行
- ✅ **代码清理**: 移除未使用的代码文件

---

*最后更新: 2025-07-21*
